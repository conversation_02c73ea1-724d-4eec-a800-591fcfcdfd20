# AWS SAM テンプレートのフォーマットバージョン
AWSTemplateFormatVersion: '2010-09-09'
# これがSAMテンプレートであることを指定します。SAM CLIはこれを使用してCloudFormationに変換します。
Transform: AWS::Serverless-2016-10-31
Description: >
  Java 21ベースのAWS Lambdaアプリケーション。
  API Gatewayによってトリガーされる関数が含まれています。

# (任意) グローバルセクション: ここで設定されたパラメータは、このテンプレート内のすべての関数に適用されます。
Globals:
  Function:
    Timeout: 60      # すべての関数のデフォルトタイムアウトを60秒に設定
    MemorySize: 512  # すべての関数のデフォルトメモリを512MBに設定

Resources:
  # これはLambda関数の論理IDです。テンプレート内で自由にカスタマイズできます。
  MyJava21Function:
    Type: AWS::Serverless::Function
    Properties:
      # CodeUriは、ソースコードとビルドスクリプト（pom.xml）を含むディレクトリを指します。
      # template.yamlがプロジェクトのルートディレクトリにある場合、通常は"."に設定します。
      CodeUri: .
      # ハンドラのフォーマット: 'パッケージ名.クラス名::メソッド名'
      Handler: com.ms.bp.interfaces.LambdaHandler
      # 重要：JDK 21用のランタイムを設定します。
      Runtime: java21
      # アーキテクチャの選択: 'arm64' (AWS Graviton) は通常コストパフォーマンスに優れています。'x86_64'も選択可能です。
      Architectures:
        - arm64

      # イベントソースの定義: このセクションでは、何がLambda関数をトリガーするかを定義します。
      Events:
        # このイベントの論理ID
        ApiEvent:
          Type: Api # タイプはAPI Gateway
          Properties:
            # API GatewayがリッスンするパスとHTTPメソッド
            Path: /api
            Method: post

    # sam buildコマンドがビルド方法を認識するためのメタデータセクション
    Metadata:
      # Mavenを使ってプロジェクトをビルドする場合
      BuildMethod: maven

# (任意) 出力セクション: デプロイ後、CloudFormationはこれらの値を出力します。
Outputs:
  # API GatewayのエンドポイントURLを出力します。
  HelloWorldApi:
    Description: "本番環境(Prod)用のAPI GatewayエンドポイントURL"
    Value: !Sub "https://#{ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/hello/"
  # Lambda関数のARNを出力します。
  MyJava21FunctionArn:
    Description: "Java 21 Lambda関数のARN"
    Value: !GetAtt MyJava21Function.Arn