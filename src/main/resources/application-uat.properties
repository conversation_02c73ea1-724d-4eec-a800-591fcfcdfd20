# Test environment configuration
# AWS configuration
aws.region=ap-northeast-1
aws.s3.bucket.name=teams-budget-app-files-test

# Database configuration
# Parameter Store settings (priority)
db.use.parameter.store=true
db.parameter.prefix=/ms-bp/uat/standard/db


# JWT configuration
jwt.secret.key=test-secret-key-for-testing

# Email configuration
email.default.from=<EMAIL>

# Frontend configuration
frontend.base.url=https://test.example.com

# Logging configuration
aws.lambda.log.format=TEXT
aws.lambda.log.level=DEBUG
