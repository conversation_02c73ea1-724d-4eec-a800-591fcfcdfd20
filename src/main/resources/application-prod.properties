# Production environment configuration
# AWS configuration
aws.region=ap-northeast-1
aws.s3.bucket.name=teams-budget-app-files-prod

# Database configuration
# Parameter Store settings (priority)
db.use.parameter.store=true
db.parameter.prefix=/ms-bp/prod/standard/db

# JWT configuration
jwt.secret.key=

# Email configuration
email.default.from=<EMAIL>

# Frontend configuration
frontend.base.url=https://app.company.com

# Logging configuration
aws.lambda.log.format=JSON
aws.lambda.log.level=INFO
