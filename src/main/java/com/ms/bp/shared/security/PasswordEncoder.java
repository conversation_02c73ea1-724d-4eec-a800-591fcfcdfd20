package com.ms.bp.shared.security;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * パスワード暗号化ユーティリティクラス
 * BCryptアルゴリズムを使用してパスワードのハッシュ化と検証を行う
 */
public class PasswordEncoder {
    private static final Logger logger = LoggerFactory.getLogger(PasswordEncoder.class);
    
    // BCryptのコスト係数（推奨値：10-12）
    private static final int BCRYPT_ROUNDS = 12;
    
    // ソルトの長さ（バイト）
    private static final int SALT_LENGTH = 16;
    
    // SHA-256を使用したハッシュ化（BCryptの代替実装）
    private static final String HASH_ALGORITHM = "SHA-256";
    
    /**
     * パスワードをハッシュ化する
     * @param plainPassword 平文パスワード
     * @return ハッシュ化されたパスワード（ソルト付き）
     */
    public static String encode(String plainPassword) {
        if (plainPassword == null || plainPassword.isEmpty()) {
            throw new IllegalArgumentException("パスワードがnullまたは空です");
        }
        
        try {
            // ランダムソルトを生成
            SecureRandom random = new SecureRandom();
            byte[] salt = new byte[SALT_LENGTH];
            random.nextBytes(salt);
            
            // パスワードとソルトを結合してハッシュ化
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
            digest.update(salt);
            byte[] hashedPassword = digest.digest(plainPassword.getBytes(StandardCharsets.UTF_8));
            
            // ソルトとハッシュをBase64エンコードして結合
            String encodedSalt = Base64.getEncoder().encodeToString(salt);
            String encodedHash = Base64.getEncoder().encodeToString(hashedPassword);
            
            // フォーマット: {salt}:{hash}
            String result = encodedSalt + ":" + encodedHash;
            
            logger.debug("パスワードのハッシュ化が完了しました");
            return result;
            
        } catch (Exception e) {
            logger.error("パスワードのハッシュ化に失敗しました", e);
            throw new RuntimeException("パスワードのハッシュ化に失敗しました", e);
        }
    }
    
    /**
     * パスワードを検証する
     * @param plainPassword 平文パスワード
     * @param hashedPassword ハッシュ化されたパスワード
     * @return 検証結果（true: 一致, false: 不一致）
     */
    public static boolean matches(String plainPassword, String hashedPassword) {
        if (plainPassword == null || hashedPassword == null) {
            return false;
        }
        
        try {
            // ハッシュ化されたパスワードからソルトとハッシュを分離
            String[] parts = hashedPassword.split(":");
            if (parts.length != 2) {
                logger.warn("無効なハッシュ形式です");
                return false;
            }
            
            String encodedSalt = parts[0];
            String encodedHash = parts[1];
            
            // ソルトをデコード
            byte[] salt = Base64.getDecoder().decode(encodedSalt);
            
            // 入力パスワードを同じソルトでハッシュ化
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
            digest.update(salt);
            byte[] hashedInput = digest.digest(plainPassword.getBytes(StandardCharsets.UTF_8));
            
            // ハッシュを比較
            String encodedInputHash = Base64.getEncoder().encodeToString(hashedInput);
            boolean matches = encodedHash.equals(encodedInputHash);
            
            logger.debug("パスワード検証結果: {}", matches);
            return matches;
            
        } catch (Exception e) {
            logger.error("パスワード検証中にエラーが発生しました", e);
            return false;
        }
    }
    
    /**
     * パスワードの強度をチェックする
     * @param password チェック対象のパスワード
     * @return 強度チェック結果
     */
    public static PasswordStrength checkPasswordStrength(String password) {
        if (password == null || password.isEmpty()) {
            return PasswordStrength.INVALID;
        }
        
        int score = 0;
        
        // 長さチェック
        if (password.length() >= 8) score++;
        if (password.length() >= 12) score++;
        
        // 文字種チェック
        if (password.matches(".*[a-z].*")) score++; // 小文字
        if (password.matches(".*[A-Z].*")) score++; // 大文字
        if (password.matches(".*[0-9].*")) score++; // 数字
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>/?].*")) score++; // 特殊文字
        
        // スコアに基づく強度判定
        return switch (score) {
            case 0, 1, 2 -> PasswordStrength.WEAK;
            case 3, 4 -> PasswordStrength.MEDIUM;
            case 5, 6 -> PasswordStrength.STRONG;
            default -> PasswordStrength.INVALID;
        };
    }
    
    /**
     * パスワード強度の列挙型
     */
    @Getter
    public enum PasswordStrength {
        INVALID("無効"),
        WEAK("弱い"),
        MEDIUM("普通"),
        STRONG("強い");
        
        private final String description;
        
        PasswordStrength(String description) {
            this.description = description;
        }

    }
}
