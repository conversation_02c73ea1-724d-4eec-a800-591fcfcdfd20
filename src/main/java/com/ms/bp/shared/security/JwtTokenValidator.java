package com.ms.bp.shared.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.shared.common.config.ConfigurationManager;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * JWT トークン検証サービス
 * 自社発行のJWTトークンの検証を行う
 */
public class JwtTokenValidator {
    private static final Logger logger = LoggerFactory.getLogger(JwtTokenValidator.class);
    private static final ConfigurationManager config = ConfigurationManager.getInstance();

    // JWT設定
    private static final String ISSUER = "ms-lambda-auth";
    private static final String SECRET_KEY = config.getProperty("jwt.secret.key", "default-secret-key-for-development");
    
    // クレーム名
    private static final String CLAIM_USER_ID = "userId";
    private static final String CLAIM_USERNAME = "username";
    private static final String CLAIM_EMAIL = "email";
    private static final String CLAIM_TOKEN_TYPE = "tokenType";
    
    // エラーメッセージ
    private static final String ERROR_TOKEN_NULL = "トークンがnullまたは空です";
    private static final String ERROR_TOKEN_INVALID_FORMAT = "トークンの形式が無効です";
    private static final String ERROR_TOKEN_EXPIRED = "トークンの有効期限が切れています";
    private static final String ERROR_TOKEN_INVALID_SIGNATURE = "トークンの署名が無効です";
    private static final String ERROR_TOKEN_INVALID_ISSUER = "トークンの発行者が無効です";
    private static final String ERROR_TOKEN_INVALID_TYPE = "トークンタイプが無効です";
    
    /**
     * JWTトークンの存在と形式をチェック
     * @param token 検証対象のトークン
     * @return 形式チェック結果
     */
    public static TokenValidationResult validateTokenFormat(String token) {
        logger.debug("トークン形式チェックを開始します");
        
        // 1. トークンの存在チェック
        if (token == null || token.trim().isEmpty()) {
            logger.warn("トークンが存在しません");
            return TokenValidationResult.failure(
                GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                ERROR_TOKEN_NULL
            );
        }
        
        // 2. Bearer プレフィックスの除去
        String actualToken = token;
        if (token.startsWith("Bearer ")) {
            actualToken = token.substring(7);
        }
        
        // 3. JWT形式チェック（3つの部分がピリオドで区切られているか）
        String[] parts = actualToken.split("\\.");
        if (parts.length != 3) {
            logger.warn("トークンの形式が無効です。期待される部分数: 3, 実際: {}", parts.length);
            return TokenValidationResult.failure(
                GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                ERROR_TOKEN_INVALID_FORMAT
            );
        }
        
        // 4. 各部分がBase64エンコードされているかチェック
        try {
            for (String part : parts) {
                if (part.isEmpty()) {
                    logger.warn("トークンの一部が空です");
                    return TokenValidationResult.failure(
                        GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                        ERROR_TOKEN_INVALID_FORMAT
                    );
                }
            }
        } catch (Exception e) {
            logger.warn("トークンのBase64デコードに失敗しました", e);
            return TokenValidationResult.failure(
                GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                ERROR_TOKEN_INVALID_FORMAT
            );
        }
        
        logger.debug("トークン形式チェック完了: 正常");
        return TokenValidationResult.success(actualToken);
    }
    
    /**
     * JWTトークンの署名を検証
     * @param token 検証対象のトークン
     * @return 署名検証結果
     */
    public static TokenValidationResult validateTokenSignature(String token) {
        logger.debug("トークン署名検証を開始します");
        
        try {
            // アルゴリズムを設定
            Algorithm algorithm = Algorithm.HMAC256(SECRET_KEY);
            
            // 署名検証のみを行う（有効期限は後でチェック）
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(ISSUER)
                    .acceptLeeway(Long.MAX_VALUE) // 有効期限チェックを一時的に無効化
                    .build();
            
            // 署名検証を実行
            DecodedJWT decodedJWT = verifier.verify(token);
            
            logger.debug("トークン署名検証完了: 正常");
            return TokenValidationResult.success(decodedJWT);
            
        } catch (JWTVerificationException e) {
            String message = e.getMessage();
            logger.warn("トークン署名検証失敗: {}", message);
            
            // エラーの種類に応じて適切なエラーコードを返す
            if (message != null && message.contains("Invalid signature")) {
                return TokenValidationResult.failure(
                    GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                    ERROR_TOKEN_INVALID_SIGNATURE
                );
            } else if (message != null && message.contains("issuer")) {
                return TokenValidationResult.failure(
                    GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                    ERROR_TOKEN_INVALID_ISSUER
                );
            } else {
                return TokenValidationResult.failure(
                    GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                    "トークンの署名検証に失敗しました"
                );
            }
        } catch (Exception e) {
            logger.error("トークン署名検証中に予期しないエラーが発生しました", e);
            return TokenValidationResult.failure(
                GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                "署名検証中にシステムエラーが発生しました"
            );
        }
    }
    
    /**
     * JWTトークンの有効期限をチェック
     * @param decodedJWT デコード済みJWT
     * @return 有効期限チェック結果
     */
    public static TokenValidationResult validateTokenExpiration(DecodedJWT decodedJWT) {
        logger.debug("トークン有効期限チェックを開始します");
        
        try {
            Date expiresAt = decodedJWT.getExpiresAt();
            Date now = new Date();
            
            if (expiresAt == null) {
                logger.warn("トークンに有効期限が設定されていません");
                return TokenValidationResult.failure(
                    GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                    "トークンに有効期限が設定されていません"
                );
            }
            
            if (expiresAt.before(now)) {
                logger.warn("トークンの有効期限が切れています。期限: {}, 現在時刻: {}", expiresAt, now);
                return TokenValidationResult.failure(
                    GlobalMessageConstants.AUTH_TOKEN_EXPIRED.getCode(),
                    ERROR_TOKEN_EXPIRED
                );
            }
            
            // 有効期限までの残り時間をログ出力
            long remainingMs = expiresAt.getTime() - now.getTime();
            long remainingMinutes = remainingMs / (1000 * 60);
            logger.debug("トークン有効期限チェック完了: 正常 (残り{}分)", remainingMinutes);
            
            return TokenValidationResult.success(decodedJWT);
            
        } catch (Exception e) {
            logger.error("トークン有効期限チェック中にエラーが発生しました", e);
            return TokenValidationResult.failure(
                GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                "有効期限チェック中にシステムエラーが発生しました"
            );
        }
    }
    
    /**
     * JWTトークンを完全に検証する（形式・署名・有効期限）
     * @param token 検証対象のトークン
     * @return 検証済みユーザー情報
     * @throws ServiceException 検証エラー
     */
    public static UserInfo validateToken(String token) throws ServiceException {
        logger.info("JWT トークンの完全検証を開始します");
        
        // 1. トークン形式チェック
        TokenValidationResult formatResult = validateTokenFormat(token);
        if (!formatResult.isValid()) {
            throw new ServiceException(formatResult.getErrorCode(), formatResult.getErrorMessage());
        }
        String cleanToken = formatResult.getToken();
        
        // 2. トークン署名検証
        TokenValidationResult signatureResult = validateTokenSignature(cleanToken);
        if (!signatureResult.isValid()) {
            throw new ServiceException(signatureResult.getErrorCode(), signatureResult.getErrorMessage());
        }
        DecodedJWT decodedJWT = signatureResult.getDecodedJWT();
        
        // 3. トークン有効期限チェック
        TokenValidationResult expirationResult = validateTokenExpiration(decodedJWT);
        if (!expirationResult.isValid()) {
            throw new ServiceException(expirationResult.getErrorCode(), expirationResult.getErrorMessage());
        }
        
        // 4. トークンタイプチェック（アクセストークンのみ許可）
        String tokenType = decodedJWT.getClaim(CLAIM_TOKEN_TYPE).asString();
        if (!"ACCESS".equals(tokenType)) {
            logger.warn("無効なトークンタイプです: {}", tokenType);
            throw new ServiceException(
                GlobalMessageConstants.AUTH_TOKEN_INVALID.getCode(),
                ERROR_TOKEN_INVALID_TYPE
            );
        }
        
        // 5. ユーザー情報を作成
        UserInfo userInfo = createUserInfoFromJWT(decodedJWT);
        
        logger.info("JWT トークン検証完了: ユーザーID={}, ユーザー名={}", 
                   userInfo.getShainCode(), userInfo.getShainCode());
        
        return userInfo;
    }
    
    /**
     * デコード済みJWTからユーザー情報を作成
     * @param jwt デコード済みJWT
     * @return ユーザー情報
     */
    private static UserInfo createUserInfoFromJWT(DecodedJWT jwt) {
        UserInfo userInfo = new UserInfo();

        // TODO: 実際のJWTトークン生成時にこれらの情報を含める必要がある
        userInfo.setShainCode(jwt.getClaim(CLAIM_USER_ID).asString());
        userInfo.setSystemOperationCompanyCode("100001");

        // 権限チェック用の情報設定



        return userInfo;
    }



    /**
     * トークン検証結果クラス
     */
    @Getter
    public static class TokenValidationResult {
        // Getters
        private final boolean valid;
        private final String token;
        private final DecodedJWT decodedJWT;
        private final Integer errorCode;
        private final String errorMessage;
        
        private TokenValidationResult(boolean valid, String token, DecodedJWT decodedJWT, 
                                    Integer errorCode, String errorMessage) {
            this.valid = valid;
            this.token = token;
            this.decodedJWT = decodedJWT;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }
        
        public static TokenValidationResult success(String token) {
            return new TokenValidationResult(true, token, null, null, null);
        }
        
        public static TokenValidationResult success(DecodedJWT decodedJWT) {
            return new TokenValidationResult(true, null, decodedJWT, null, null);
        }
        
        public static TokenValidationResult failure(Integer errorCode, String errorMessage) {
            return new TokenValidationResult(false, null, null, errorCode, errorMessage);
        }

    }
}
