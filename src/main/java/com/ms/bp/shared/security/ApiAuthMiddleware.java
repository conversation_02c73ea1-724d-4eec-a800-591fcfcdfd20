package com.ms.bp.shared.security;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * API認証ミドルウェア
 * 自社発行のJWTトークンによるAPI認証を行う
 */
public class ApiAuthMiddleware {
    private static final Logger logger = LoggerFactory.getLogger(ApiAuthMiddleware.class);

    /**
     * JWTトークンを検証してユーザー情報を取得する
     * @param token JWTトークン
     * @return 検証済みユーザー情報
     * @throws ServiceException 認証エラー
     */
    public static UserInfo validateJwtToken(String token) throws ServiceException {

        try {
            // JwtTokenValidatorを使用してトークンを検証
            return JwtTokenValidator.validateToken(token);
            
        } catch (ServiceException e) {
            // ServiceExceptionはそのまま再スロー
            logger.warn("JWT トークン検証失敗: {}", e.getMessage());
            throw e;
            
        } catch (Exception e) {
            // その他の例外はシステムエラーとして処理
            logger.error("JWT トークン検証中に予期しないエラーが発生しました", e);
            throw new ServiceException(
                GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                "認証処理中にシステムエラーが発生しました"
            );
        }
    }

}
