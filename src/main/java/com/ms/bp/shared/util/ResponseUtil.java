package com.ms.bp.shared.util;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.ms.bp.shared.common.CommonResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * API Gateway レスポンスユーティリティクラス
 * CommonResult を APIGatewayProxyResponseEvent に変換する
 */
public class ResponseUtil {
    private static final Logger logger = LoggerFactory.getLogger(ResponseUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // CORSヘッダー定数
    private static final String HEADER_ORIGIN = "Access-Control-Allow-Origin";
    private static final String HEADER_CREDENTIALS = "Access-Control-Allow-Credentials";
    private static final String CONTENT_TYPE = "Content-Type";

    /**
     * CommonResultをAPIGatewayProxyResponseEventに変換する
     * @param result 共通レスポンス結果
     * @return API Gatewayレスポンス
     */
    public static APIGatewayProxyResponseEvent toApiGatewayResponse(CommonResult<?> result) {
        try {
            String body = objectMapper.writeValueAsString(result);
            return createApiGatewayResponse(200, body);
        } catch (JsonProcessingException e) {
            logger.error("JSONシリアル化エラー", e);
            return createInternalErrorResponse();
        }
    }

    /**
     * APIGatewayProxyResponseEventを初期化する共通メソッド
     * @param statusCode HTTPステータスコード
     * @param body レスポンスボディ
     * @return 初期化されたAPIGatewayProxyResponseEvent
     */
    private static APIGatewayProxyResponseEvent createApiGatewayResponse(int statusCode, String body) {
        var response = new APIGatewayProxyResponseEvent();
        response.setStatusCode(statusCode);

        var headers = new HashMap<String, String>();
        headers.put(CONTENT_TYPE, "application/json");
        headers.put(HEADER_ORIGIN, "*");
        headers.put(HEADER_CREDENTIALS, "true");
        response.setHeaders(headers);

        response.setBody(body);
        return response;
    }

    /**
     * 内部サーバーエラーレスポンスを生成する
     * @return API Gatewayレスポンス
     */
    private static APIGatewayProxyResponseEvent createInternalErrorResponse() {
        try {
            CommonResult<?> errorResult = CommonResult.error(GlobalMessageConstants.INTERNAL_SERVER_ERROR);
            String body = objectMapper.writeValueAsString(errorResult);
            return createApiGatewayResponse(500, body);
        } catch (JsonProcessingException e) {
            // この極端なケースでは、事前に構築された文字列を使用する
            logger.error("重大なエラー: エラーレスポンスの生成に失敗しました", e);
            return createApiGatewayResponse(500, "{\"code\":500,\"msg\":\"Internal Server Error\",\"data\":null}");
        }
    }
}