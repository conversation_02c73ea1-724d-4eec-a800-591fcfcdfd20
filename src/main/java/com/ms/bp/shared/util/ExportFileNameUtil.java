package com.ms.bp.shared.util;

import com.ms.bp.shared.common.constants.BusinessConstants;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * エクスポートファイル名生成ユーティリティクラス
 * 全てのExportServiceで共通して使用可能
 */
public class ExportFileNameUtil {
    
    /**
     * デフォルトファイル名を生成
     * @param prefix プレフィックス
     * @return 生成されたファイル名
     */
    public static String generateDefaultFileName(String prefix) {
        return prefix + "_" + DateUtil.getCurrentDateTimeString() + ".csv";
    }

    /**
     * ファイル種別に応じたS3ディレクトリパスを取得
     *
     * @param fileType ファイル種別コード
     * @return S3ディレクトリパス
     * @throws IllegalArgumentException サポートされていないファイル種別の場合
     */
    public static String getS3DirectoryByFileType(String fileType) {
        return switch (fileType) {
            case BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE -> // 次年度計画マスタ
                    BusinessConstants.S3_INPUT_DIR_PLAN_MASTER + DateUtil.getCurrentDateString();
            case BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE -> // 見通し・計画_採算管理単位C別＜本社＞
                    BusinessConstants.S3_INPUT_DIR_BUDGET_HONSHA + DateUtil.getCurrentDateString();
            case BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE -> // 見通し・計画_採算管理単位C別＜エリア＞
                    BusinessConstants.S3_INPUT_DIR_BUDGET_AREA + DateUtil.getCurrentDateString();
            case BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE -> // 間接利益計画_メーカー別
                    BusinessConstants.S3_INPUT_DIR_INDIRECT_PROFIT + DateUtil.getCurrentDateString();
            default -> throw new IllegalArgumentException("サポートされていないファイル種別: " + fileType);
        };
    }

    /**
     * ファイル種別に応じたファイル名を取得
     *
     * @param fileType ファイル種別コード
     * @return ファイル名
     * @throws IllegalArgumentException サポートされていないファイル種別の場合
     */
    public static String getFileNameByFileType(String fileType) {
        return switch (fileType) {
            case BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE -> // 次年度計画マスタ
                    BusinessConstants.FILE_NAME_PLAN_MASTER;
            case BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE -> // 見通し・計画_採算管理単位C別＜本社＞
                    BusinessConstants.FILE_NAME_BUDGET_HONSHA;
            case BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE -> // 見通し・計画_採算管理単位C別＜エリア＞
                    BusinessConstants.FILE_NAME_BUDGET_AREA;
            case BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE -> // 間接利益計画_メーカー別
                    BusinessConstants.FILE_NAME_INDIRECT_PROFIT;
            default -> throw new IllegalArgumentException("サポートされていないファイル種別: " + fileType);
        };
    }

    /**
     * データタイプに応じたS3ディレクトリパスを取得
     * @param dataType データタイプ
     * @return S3ディレクトリパス
     */
    public static String getS3DirectoryByDataType(String dataType) {
        return switch (dataType.trim()) {
            case BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE -> // 次年度計画マスタ
                    BusinessConstants.S3_DIR_PLAN_MASTER + DateUtil.getCurrentDateString();
            case BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE -> // 見通し・計画_採算管理単位C別＜本社＞
                    BusinessConstants.S3_DIR_BUDGET_HONSHA + DateUtil.getCurrentDateString();
            case BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE -> // 見通し・計画_採算管理単位C別＜エリア＞
                    BusinessConstants.S3_DIR_BUDGET_AREA + DateUtil.getCurrentDateString();
            case BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE -> // 間接利益計画_メーカー別
                    BusinessConstants.S3_DIR_INDIRECT_PROFIT + DateUtil.getCurrentDateString();
            default -> throw new IllegalArgumentException("サポートされていないデータタイプ: " + dataType);
        };
    }

}
