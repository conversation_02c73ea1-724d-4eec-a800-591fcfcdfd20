package com.ms.bp.shared.util;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * リクエストオブジェクト変換ユーティリティクラス
 * 通用的なリクエストオブジェクトの型変換機能を提供
 */
public class RequestConversionUtil {

    private static final Logger logger = LoggerFactory.getLogger(RequestConversionUtil.class);

    /** JSON変換用のObjectMapperインスタンス（スレッドセーフ） */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 汎用リクエストオブジェクト変換メソッド
     * 任意のオブジェクトを指定されたターゲットクラスに変換する
     * APIGatewayProxyRequestEventの場合はbodyフィールドから変換する
     *
     * @param <T> ターゲットクラスの型
     * @param requestObject 変換対象のリクエストオブジェクト
     * @param targetClass 変換先のクラス
     * @return T 変換されたオブジェクト
     * @throws ServiceException 変換に失敗した場合
     */
    public static <T> T convertRequest(Object requestObject, Class<T> targetClass) {
        try {
            // null チェック
            if (requestObject == null) {
                throw new IllegalArgumentException("リクエストオブジェクトがnullです");
            }
            if (targetClass == null) {
                throw new IllegalArgumentException("ターゲットクラスがnullです");
            }

            // 既に目標の型の場合は直接キャストして返す
            if (targetClass.isInstance(requestObject)) {
                return targetClass.cast(requestObject);
            }

            String json;

            // APIGatewayProxyRequestEventの場合はbodyフィールドから変換
            if (requestObject instanceof APIGatewayProxyRequestEvent apiRequest) {
                json = apiRequest.getBody();
                if (json == null || json.trim().isEmpty()) {
                    throw new IllegalArgumentException("APIGatewayProxyRequestEventのbodyが空です");
                }
            } else {
                // その他のオブジェクトの場合は全体をJSON化
                json = OBJECT_MAPPER.writeValueAsString(requestObject);
            }

            return OBJECT_MAPPER.readValue(json, targetClass);

        } catch (Exception e) {
            logger.error("汎用リクエスト変換エラー: ターゲットクラス={}, エラー={}",
                        targetClass != null ? targetClass.getSimpleName() : "null", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "リクエストオブジェクトの変換に失敗しました: " + e.getMessage());
        }
    }
}
