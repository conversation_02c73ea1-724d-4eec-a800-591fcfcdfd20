package com.ms.bp.shared.util;

import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import lombok.Getter;

/**
 * 機能ID列挙型
 * 操作タイプとファイル種別の組み合わせに基づいて機能IDを動的に決定する
 * 各機能IDは以下の要素で構成される：
 * - 機能ID：システム内で一意の機能識別子
 * - データタイプ：ファイル種別（1,2,3,4）
 * - 操作区分：操作タイプ（1:導入、2:エクスポート）
 * - 説明：機能の日本語説明
 */
@Getter
public enum FunctionUtil {
    
    // ==================== 導入操作（操作区分：1） ====================
    
    /**
     * 次年度計画マスタ導入機能
     * データタイプ：1、操作区分：1
     */
    IMPORT_PLAN_MASTER("BAT_005", "1", "1", "次年度計画マスタ導入"),
    
    /**
     * 見通・計画_予算管理表(G/R)/見通・計画(企業別)<本社>導入機能
     * データタイプ：2、操作区分：1
     */
    IMPORT_BUDGET_HONSHA("BAT_006", "2", "1", "見通・計画_予算管理表(G/R)/見通・計画(企業別)<本社>導入"),
    
    /**
     * 見通・計画_予算管理表(G/R)/見通・計画(企業別)<エリア>導入機能
     * データタイプ：3、操作区分：1
     */
    IMPORT_BUDGET_AREA("BAT_006", "3", "1", "見通・計画_予算管理表(G/R)/見通・計画(企業別)<エリア>導入"),
    
    /**
     * 間接利益計画_メーカー別導入機能
     * データタイプ：4、操作区分：1
     */
    IMPORT_INDIRECT_PROFIT("BAT_007", "4", "1", "間接利益計画_メーカー別導入"),
    
    // ==================== エクスポート操作（操作区分：2） ====================
    
    /**
     * 次年度計画マスタエクスポート機能
     * データタイプ：1、操作区分：2
     */
    EXPORT_PLAN_MASTER("BAT_001", "1", "2", "次年度計画マスタエクスポート"),
    
    /**
     * 見通・計画_予算管理表(G/R)/見通・計画(企業別)<本社>エクスポート機能
     * データタイプ：2、操作区分：2
     */
    EXPORT_BUDGET_HONSHA("BAT_002", "2", "2", "見通・計画_予算管理表(G/R)/見通・計画(企業別)<本社>エクスポート"),
    
    /**
     * 見通・計画_予算管理表(G/R)/見通・計画(企業別)<エリア>エクスポート機能
     * データタイプ：3、操作区分：2
     */
    EXPORT_BUDGET_AREA("BAT_002", "3", "2", "見通・計画_予算管理表(G/R)/見通・計画(企業別)<エリア>エクスポート"),
    
    /**
     * 間接利益計画_メーカー別エクスポート機能
     * データタイプ：4、操作区分：2
     */
    EXPORT_INDIRECT_PROFIT("BAT_004", "4", "2", "間接利益計画_メーカー別エクスポート");
    
    // ==================== 列挙型属性 ====================
    
    /**
     * 機能ID（システム内で一意の識別子）
     * -- GETTER --
     *  機能IDを取得

     */
    private final String functionId;
    
    /**
     * データタイプ（ファイル種別）
     * 1: 次年度計画マスタ
     * 2: 見通・計画_予算管理表(G/R)/見通・計画(企業別)<本社>
     * 3: 見通・計画_予算管理表(G/R)/見通・計画(企業別)<エリア>
     * 4: 間接利益計画_メーカー別
     * -- GETTER --
     *  データタイプを取得
     *

     */
    private final String dataType;
    
    /**
     * 操作区分
     * 1: 導入（インポート）
     * 2: エクスポート
     * -- GETTER --
     *  操作区分を取得

     */
    private final String operationDivision;
    
    /**
     * 機能の説明（日本語）
     * -- GETTER --
     *  機能説明を取得

     */
    private final String description;
    
    // ==================== コンストラクタ ====================
    
    /**
     * 機能ID列挙型のコンストラクタ
     * 
     * @param functionId 機能ID
     * @param dataType データタイプ
     * @param operationDivision 操作区分
     * @param description 機能説明
     */
    FunctionUtil(String functionId, String dataType, String operationDivision, String description) {
        this.functionId = functionId;
        this.dataType = dataType;
        this.operationDivision = operationDivision;
        this.description = description;
    }
    
    // ==================== ゲッターメソッド ====================

    // ==================== 静的メソッド ====================
    
    /**
     * 操作タイプとデータタイプに基づいて機能IDを取得
     * 
     * @param operationType 操作タイプ（"IMPORT" または "EXPORT"）
     * @param dataType データタイプ（"1", "2", "3", "4"）
     * @return 対応する機能ID列挙値
     * @throws ServiceException 無効な組み合わせの場合
     */
    public static FunctionUtil getFunctionId(String operationType, String dataType) throws ServiceException {
        // 機能IDを検索
        for (FunctionUtil functionId : values()) {
            if (functionId.dataType.equals(dataType) && 
                functionId.operationDivision.equals(operationType)) {
                return functionId;
            }
        }
        
        // 該当する機能IDが見つからない場合はエラー
        throw new ServiceException(
            GlobalMessageConstants.BUSINESS_ERROR.getCode(),
            String.format("指定された組み合わせに対応する機能IDが見つかりません。操作タイプ：%s、データタイプ：%s", 
                         operationType, dataType)
        );
    }
    


}
