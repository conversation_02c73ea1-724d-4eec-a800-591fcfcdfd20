package com.ms.bp.shared.util;

import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ZIPファイル処理ユーティリティ
 * ZIP操作の汎用的な処理を提供
 */
public class ZipUtil {
    private static final Logger logger = LoggerFactory.getLogger(ZipUtil.class);
    private static final int BUFFER_SIZE = 8192; // 8KB buffer

    /**
     * 複数のファイルをZIPアーカイブに圧縮する
     *
     * @param filesToZip 圧縮するファイルパスのリスト
     * @param outputZipFile ZIPファイルの出力パス
     * @throws IOException ZIP処理中のエラー
     */
    public static void zipFiles(List<Path> filesToZip, Path outputZipFile) throws IOException {
        logger.info("Zipping {} files to: {}", filesToZip.size(), outputZipFile);
        byte[] buffer = new byte[BUFFER_SIZE];

        try (FileOutputStream fos = new FileOutputStream(outputZipFile.toFile());
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            for (Path file : filesToZip) {
                if (!file.toFile().exists()) {
                    logger.warn("File not found, skipping: {}", file);
                    continue;
                }

                // ZIPエントリ名としてファイル名のみを使用
                ZipEntry zipEntry = new ZipEntry(file.getFileName().toString());
                zos.putNextEntry(zipEntry);

                try (FileInputStream fis = new FileInputStream(file.toFile())) {
                    int length;
                    while ((length = fis.read(buffer)) >= 0) {
                        zos.write(buffer, 0, length);
                    }
                }
                zos.closeEntry();
                logger.debug("Added {} to zip", file.getFileName());
            }
            logger.info("Successfully zipped {} files.", filesToZip.size());

        } catch (IOException e) {
            logger.error("Error zipping files to {}: {}", outputZipFile, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * CSVファイルリストからZIPファイルを作成（高レベルAPI）
     * @param csvFiles CSVファイルリスト
     * @param zipFileName ZIPファイル名（拡張子なし）
     * @return 作成されたZIPファイルのパス
     */
    public static Path createZip(List<Path> csvFiles, String zipFileName) {
        try {
            if (csvFiles == null || csvFiles.isEmpty()) {
                throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                        "ZIPに含めるファイルが指定されていません");
            }

            // 一時ディレクトリに指定されたファイル名でZIPファイルを作成
            Path tempDir = Files.createTempDirectory("zip_temp");
            Path tempZipFile = tempDir.resolve(zipFileName + ".zip");

            // ZIPファイルを作成
            zipFiles(csvFiles, tempZipFile);

            logger.info("ZIPファイル作成完了: {}, ファイル数: {}", zipFileName, csvFiles.size());

            return tempZipFile;

        } catch (IOException e) {
            logger.error("ZIPファイル作成エラー: {}", zipFileName, e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "ZIPファイルの作成に失敗しました: " + e.getMessage());
        }
    }

    /**
     * 一時ファイルをクリーンアップ
     * @param csvFiles CSVファイルリスト
     * @param zipFile ZIPファイル
     */
    public static void cleanUpTempFiles(List<Path> csvFiles, Path zipFile) {
        if (csvFiles != null) {
            for (Path file : csvFiles) {
                try {
                    Files.deleteIfExists(file);
                    logger.debug("CSV一時ファイルを削除しました: {}", file);
                } catch (IOException e) {
                    logger.warn("CSV一時ファイルの削除に失敗しました {}: {}", file, e.getMessage());
                }
            }
        }

        if (zipFile != null) {
            try {
                Files.deleteIfExists(zipFile);
                logger.debug("ZIP一時ファイルを削除しました: {}", zipFile);

                // 一時ディレクトリも削除
                Path parentDir = zipFile.getParent();
                if (parentDir != null && parentDir.getFileName().toString().startsWith("zip_temp")) {
                    Files.deleteIfExists(parentDir);
                    logger.debug("ZIP一時ディレクトリを削除しました: {}", parentDir);
                }
            } catch (IOException e) {
                logger.warn("ZIP一時ファイルの削除に失敗しました {}: {}", zipFile, e.getMessage());
            }
        }
    }
}