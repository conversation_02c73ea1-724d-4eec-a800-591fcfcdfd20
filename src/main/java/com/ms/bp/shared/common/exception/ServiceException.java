package com.ms.bp.shared.common.exception;

import lombok.Getter;

/**
 * ビジネスロジック例外 Exception
 */
public class ServiceException extends RuntimeException {

    /**
     * ビジネスエラー
     */
    @Getter
    private Integer code;
    /**
     * エラーメッセージ
     */
    private String message;

    /**
     * 空のコンストラクタ、逆シリアル化問題を避けるため
     */
    public ServiceException() {
    }

    public ServiceException(Message message) {
        this.code = message.getCode();
        this.message = message.getMsg();
    }

    public ServiceException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * パラメータ化されたエラーコードを使用するコンストラクタ
     *
     * @param parameterizedErrorCode パラメータ化されたエラーコード
     */
    public ServiceException(ParameterizedErrorCode parameterizedErrorCode) {
        this.code = parameterizedErrorCode.getCode();
        this.message = parameterizedErrorCode.getFormattedMessage();
    }

    /**
     * エラーコードとパラメータを使用するコンストラクタ
     * エラーコードのメッセージをテンプレートとして使用し、パラメータで置換
     *
     * @param message エラーコード（メッセージテンプレート）
     * @param parameters メッセージパラメータ
     */
    public ServiceException(Message message, Object... parameters) {
        ParameterizedErrorCode parameterizedErrorCode = new ParameterizedErrorCode(message, parameters);
        this.code = parameterizedErrorCode.getCode();
        this.message = parameterizedErrorCode.getFormattedMessage();
    }

    public ServiceException setCode(Integer code) {
        this.code = code;
        return this;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public ServiceException setMessage(String message) {
        this.message = message;
        return this;
    }
}