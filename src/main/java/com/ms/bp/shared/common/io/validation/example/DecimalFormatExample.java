package com.ms.bp.shared.common.io.validation.example;

import com.ms.bp.shared.common.io.validation.annotation.DecimalFormat;
import com.ms.bp.shared.common.io.validation.annotation.Required;
import com.ms.bp.shared.common.io.validation.AnnotationValidator;
import com.ms.bp.shared.common.exception.ValidationError;
import lombok.Data;

import java.util.List;

/**
 * DecimalFormat注解の使用例
 * 数値フォーマット校验器の動作を示すサンプルクラス
 */
public class DecimalFormatExample {

    /**
     * 数値フォーマット校验のテスト用データクラス
     */
    @Data
    public static class PriceData {
        
        /**
         * 商品価格 - 1.00から999.99の範囲で小数点第2位まで必須
         */
        @Required(fieldName = "商品価格")
        @DecimalFormat(fieldName = "商品価格")
        private String price;
        
        /**
         * 割引率 - カスタムエラーメッセージ
         */
        @DecimalFormat(
            fieldName = "割引率", 
            message = "割引率は1.00から999.99の範囲で小数点第2位まで入力してください"
        )
        private String discountRate;
        
        /**
         * 税率 - カスタムエラーコード
         */
        @DecimalFormat(
            fieldName = "税率",
            errorCode = "CUSTOM_002"
        )
        private String taxRate;
    }

    /**
     * 数値フォーマット校验のテスト実行例
     */
    public static void main(String[] args) {
        System.out.println("=== 数値フォーマット校验テスト ===\n");

        // 有効データのテスト
        testValidData();
        
        // 無効データのテスト
        testInvalidData();
    }

    /**
     * 有効データの校验テスト
     */
    private static void testValidData() {
        System.out.println("--- 有効データの校验 ---");
        
        String[] validValues = {
            "1.00",      // 最小値
            "123.45",    // 中間値
            "999.99"     // 最大値
        };

        for (String value : validValues) {
            PriceData data = new PriceData();
            data.setPrice(value);
            data.setDiscountRate(value);
            data.setTaxRate(value);

            List<ValidationError> errors = AnnotationValidator.validate(data);
            if (errors.isEmpty()) {
                System.out.println("✓ " + value + " - 検証成功");
            } else {
                System.out.println("✗ " + value + " - 検証失敗:");
                errors.forEach(error -> System.out.println("  " + error.getMessage()));
            }
        }
        System.out.println();
    }

    /**
     * 無効データの校验テスト
     */
    private static void testInvalidData() {
        System.out.println("--- 無効データの校验 ---");
        
        String[][] invalidTestCases = {
            {"0.99", "最小値未満"},
            {"1000.00", "最大値超過"},
            {"1.1", "小数部分1桁"},
            {"1.123", "小数部分3桁"},
            {"1", "小数点なし"},
            {"abc.12", "非数値文字"},
            {"12.ab", "小数部分に非数値"},
            {"-1.00", "負数"},
            {"1.00.00", "小数点複数"},
            {" 1.00", "先頭空白"},
            {"1.00 ", "末尾空白"}
        };

        for (String[] testCase : invalidTestCases) {
            String value = testCase[0];
            String description = testCase[1];
            
            PriceData data = new PriceData();
            data.setPrice(value);

            List<ValidationError> errors = AnnotationValidator.validate(data);
            System.out.println("✗ " + value + " (" + description + "):");
            if (errors.isEmpty()) {
                System.out.println("  予期しない検証成功");
            } else {
                errors.forEach(error -> System.out.println("  " + error.getMessage()));
            }
            System.out.println();
        }
    }
}
