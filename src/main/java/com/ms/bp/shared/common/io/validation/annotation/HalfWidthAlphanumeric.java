package com.ms.bp.shared.common.io.validation.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 半角英数字（a-z, A-Z, 0-9）のみを許可するアノテーション
 * GlobalCodeConstants.ERR_019と統合されたバリデーション機能を提供
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface HalfWidthAlphanumeric {

    /**
     * カスタム日本語フィールド名
     * 指定された場合、エラーメッセージでJavaフィールド名の代わりに使用される
     * 例：fieldName = "ユーザーID"
     */
    String fieldName() default "";

    /**
     * エラーコード（デフォルト：ERR_019）
     * GlobalCodeConstantsで定義されたエラーコードを指定
     */
    String errorCode() default "ERR_019";

    /**
     * カスタムエラーメッセージ
     * 空の場合はerrorCodeに対応するGlobalCodeConstantsのメッセージテンプレートを使用
     * 指定された場合は{field}占位符をサポート
     */
    String message() default "";
}
