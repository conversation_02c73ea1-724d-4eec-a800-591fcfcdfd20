package com.ms.bp.shared.common.db;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * JDBC操作のヘルパークラス
 * トランザクション管理とSQL注入防護を提供
 */
public class JdbcTemplate implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(JdbcTemplate.class);

    private final Connection connection;
    private final boolean autoCommit;
    private boolean inTransaction = false;

    /**
     * コンストラクタ
     */
    public JdbcTemplate(Connection connection) throws SQLException {
        this.connection = connection;
        this.autoCommit = connection.getAutoCommit();
    }

    /**
     * トランザクションを開始
     */
    public void beginTransaction() throws SQLException {
        if (inTransaction) {
            throw new IllegalStateException("既にトランザクション内です");
        }
        connection.setAutoCommit(false);
        inTransaction = true;
    }

    /**
     * トランザクションをコミット
     */
    public void commit() throws SQLException {
        if (!inTransaction) {
            throw new IllegalStateException("トランザクションが開始されていません");
        }
        connection.commit();
        inTransaction = false;
        connection.setAutoCommit(autoCommit);
    }

    /**
     * トランザクションをロールバック
     */
    public void rollback() {
        try {
            if (inTransaction && !connection.isClosed()) {
                connection.rollback();
                inTransaction = false;
                connection.setAutoCommit(autoCommit);
            }
        } catch (SQLException e) {
            logger.error("ロールバック中にエラーが発生しました", e);
        }
    }

    /**
     * トランザクション内で処理を実行
     */
    public <T> T executeInTransaction(TransactionCallback<T> callback) throws SQLException {
        beginTransaction();
        try {
            T result = callback.doInTransaction(this);
            commit();
            return result;
        } catch (Exception e) {
            rollback();
            throw new SQLException("トランザクション処理中にエラーが発生しました", e);
        }
    }

    /**
     * SELECT文を実行してリストを取得
     */
    public <T> List<T> query(String sql, Object[] params, RowMapper<T> rowMapper) throws SQLException {
        List<T> results = new ArrayList<>();

        try (PreparedStatement ps = prepareStatement(sql, params);
             ResultSet rs = ps.executeQuery()) {

            while (rs.next()) {
                results.add(rowMapper.mapRow(rs));
            }
        }

        return results;
    }

    /**
     * SELECT文を実行して単一のオブジェクトを取得
     */
    public <T> T queryForObject(String sql, Object[] params, RowMapper<T> rowMapper) throws SQLException {
        List<T> results = query(sql, params, rowMapper);
        if (results.isEmpty()) {
            return null;
        }
        if (results.size() > 1) {
            throw new SQLException("クエリが複数の結果を返しました");
        }
        return results.getFirst();
    }

    /**
     * COUNT等の集計クエリを実行
     */
    public Long queryForLong(String sql, Object[] params) throws SQLException {
        return queryForObject(sql, params, rs -> rs.getLong(1));
    }

    /**
     * INSERT/UPDATE/DELETE文を実行
     */
    public int update(String sql, Object[] params) throws SQLException {
        try (PreparedStatement ps = prepareStatement(sql, params)) {
            return ps.executeUpdate();
        }
    }

    /**
     * INSERT文を実行して自動生成されたキーを返す
     * @param sql INSERT文
     * @param params パラメータ配列
     * @param keyColumn 自動生成されるキーのカラム名
     * @return 自動生成されたキー値
     * @throws SQLException SQL実行エラー
     */
    public Long insertWithGeneratedKey(String sql, Object[] params, String keyColumn) throws SQLException {
        try (PreparedStatement ps = connection.prepareStatement(sql, new String[]{keyColumn})) {
            setParameters(ps, params);
            int result = ps.executeUpdate();

            if (result != 1) {
                throw new SQLException("挿入に失敗しました。影響を受けた行数: " + result);
            }

            try (ResultSet rs = ps.getGeneratedKeys()) {
                if (rs.next()) {
                    return rs.getLong(1);
                } else {
                    throw new SQLException("自動生成されたキーの取得に失敗しました");
                }
            }
        }
    }

    /**
     * バッチ更新を実行
     */
    public int[] batchUpdate(String sql, List<Object[]> paramsList) throws SQLException {
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            for (Object[] params : paramsList) {
                setParameters(ps, params);
                ps.addBatch();
            }
            return ps.executeBatch();
        }
    }

    /**
     * バッチ挿入を実行（自動生成されたキーを返す）
     */
    public List<Long> batchInsertWithGeneratedKeys(String sql, List<Object[]> paramsList, String keyColumn)
            throws SQLException {
        List<Long> generatedKeys = new ArrayList<>();

        try (PreparedStatement ps = connection.prepareStatement(sql, new String[]{keyColumn})) {
            for (Object[] params : paramsList) {
                setParameters(ps, params);
                ps.addBatch();
            }

            ps.executeBatch();

            try (ResultSet rs = ps.getGeneratedKeys()) {
                while (rs.next()) {
                    generatedKeys.add(rs.getLong(1));
                }
            }
        }

        return generatedKeys;
    }

    /**
     * テーブルを切り捨て（TRUNCATE）
     */
    public void truncateTable(String tableName) throws SQLException {
        // テーブル名の検証（SQL注入防止）
        validateTableName(tableName);
        String sql = "TRUNCATE TABLE " + tableName;

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sql);
        }
    }

    /**
     * レコードの存在確認
     */
    public boolean exists(String tableName, String keyColumn, Object keyValue) throws SQLException {
        validateTableName(tableName);
        validateColumnName(keyColumn);

        String sql = "SELECT 1 FROM " + tableName + " WHERE " + keyColumn + " = ? LIMIT 1";
        List<Integer> result = query(sql, new Object[]{keyValue}, rs -> 1);
        return !result.isEmpty();
    }
    /**
     * 複合主キーで既存レコードを検索
     *
     * @param tableName テーブル名
     * @param keyColumns キー列のリスト
     * @param keyValuesMap キー文字列と値のリストのマップ
     * @return 存在するキー文字列のセット
     */
    public Set<String> findExistingCompositeKeys(
            String tableName,
            List<String> keyColumns,
            Map<String, List<Object>> keyValuesMap) throws SQLException {

        if (keyValuesMap.isEmpty()) {
            return new HashSet<>();
        }

        Set<String> existingKeys = new HashSet<>();

        // WHERE句を構築
        List<String> whereConditions = keyColumns.stream()
                .map(col -> col + " = ?")
                .collect(Collectors.toList());
        String whereClause = "(" + String.join(" AND ", whereConditions) + ")";

        // バッチサイズを考慮してクエリを分割
        int batchSize = 100;
        List<Map.Entry<String, List<Object>>> entries = new ArrayList<>(keyValuesMap.entrySet());

        for (int i = 0; i < entries.size(); i += batchSize) {
            List<Map.Entry<String, List<Object>>> batch = entries.subList(i,
                    Math.min(i + batchSize, entries.size()));

            // IN句の代わりにOR条件を使用
            List<String> orConditions = new ArrayList<>();
            List<Object> params = new ArrayList<>();

            for (Map.Entry<String, List<Object>> entry : batch) {
                orConditions.add(whereClause);
                params.addAll(entry.getValue());
            }

            String sql = String.format("SELECT %s FROM %s WHERE %s",
                    String.join(", ", keyColumns),
                    tableName,
                    String.join(" OR ", orConditions));

            try (PreparedStatement ps = connection.prepareStatement(sql)) {
                // パラメータを設定
                for (int j = 0; j < params.size(); j++) {
                    ps.setObject(j + 1, params.get(j));
                }

                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        // 結果からキー文字列を再構築
                        List<String> keyValues = new ArrayList<>();
                        for (String keyColumn : keyColumns) {
                            Object value = rs.getObject(keyColumn);
                            keyValues.add(value != null ? value.toString() : "null");
                        }
                        existingKeys.add(String.join("_", keyValues));
                    }
                }
            }
        }

        return existingKeys;
    }
    /**
     * 複数のキーの存在確認（バッチ）
     */
    public Set<Object> findExistingKeys(String tableName, String keyColumn, Collection<Object> keys)
            throws SQLException {
        if (keys.isEmpty()) {
            return new HashSet<>();
        }

        validateTableName(tableName);
        validateColumnName(keyColumn);

        // IN句用のプレースホルダーを作成
        String placeholders = String.join(",", Collections.nCopies(keys.size(), "?"));
        String sql = "SELECT " + keyColumn + " FROM " + tableName +
                " WHERE " + keyColumn + " IN (" + placeholders + ")";

        Set<Object> existingKeys = new HashSet<>();
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            int index = 1;
            for (Object key : keys) {
                ps.setObject(index++, key);
            }

            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    existingKeys.add(rs.getObject(1));
                }
            }
        }

        return existingKeys;
    }


    /**
     * PreparedStatementを準備
     */
    private PreparedStatement prepareStatement(String sql, Object[] params) throws SQLException {
        PreparedStatement ps = connection.prepareStatement(sql);
        setParameters(ps, params);
        return ps;
    }

    /**
     * パラメータを設定
     */
    private void setParameters(PreparedStatement ps, Object[] params) throws SQLException {
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
        }
    }

    /**
     * テーブル名の検証（SQL注入防止）
     */
    private void validateTableName(String tableName) {
        if (!tableName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            throw new IllegalArgumentException("無効なテーブル名: " + tableName);
        }
    }

    /**
     * カラム名の検証（SQL注入防止）
     */
    private void validateColumnName(String columnName) {
        if (!columnName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$")) {
            throw new IllegalArgumentException("無効なカラム名: " + columnName);
        }
    }

    /**
     * リソースを静かに閉じる
     */
    private void closeQuietly(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                logger.warn("リソースのクローズ中にエラーが発生しました", e);
            }
        }
    }

    @Override
    public void close() {
        try {
            if (inTransaction) {
                rollback();
            }
            if (!connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            logger.error("接続のクローズ中にエラーが発生しました", e);
        }
    }

    /**
     * 行マッパーインターフェース
     */
    @FunctionalInterface
    public interface RowMapper<T> {
        T mapRow(ResultSet rs) throws SQLException;
    }

    /**
     * トランザクションコールバックインターフェース
     */
    @FunctionalInterface
    public interface TransactionCallback<T> {
        T doInTransaction(JdbcTemplate template) throws Exception;
    }
}