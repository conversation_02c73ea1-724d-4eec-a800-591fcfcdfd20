package com.ms.bp.shared.common.io.validation.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数値フォーマット（1.00-999.99）を校験するアノテーション
 * 整数部分1-3桁、小数部分必須2桁の形式を強制し、指定範囲内の数値のみを許可
 * GlobalCodeConstants.ERR_020と統合されたバリデーション機能を提供
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DecimalFormat {

    /**
     * カスタム日本語フィールド名
     * 指定された場合、エラーメッセージでJavaフィールド名の代わりに使用される
     * 例：fieldName = "金額"
     */
    String fieldName() default "";

    /**
     * エラーコード（デフォルト：ERR_020）
     * GlobalCodeConstantsで定義されたエラーコードを指定
     */
    String errorCode() default "ERR_020";

    /**
     * カスタムエラーメッセージ
     * 空の場合はerrorCodeに対応するGlobalCodeConstantsのメッセージテンプレートを使用
     * 指定された場合は{field}占位符をサポート
     */
    String message() default "";
}
