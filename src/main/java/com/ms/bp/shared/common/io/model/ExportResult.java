package com.ms.bp.shared.common.io.model;

import lombok.Getter;
import lombok.Setter;

import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * エクスポート結果クラス
 */
@Setter
@Getter
public class ExportResult {
    private int exportedCount;  // エクスポートされた件数
    private Path filePath;
    private List<String> errors;  // エラー情報のリスト
    private Map<String, Object> statistics;  // 統計情報

    public ExportResult() {
        this.exportedCount = 0;
        this.errors = new ArrayList<>();
        this.statistics = new HashMap<>();
    }

    /**
     * エラーを追加する
     */
    public void addError(String error) {
        this.errors.add(error);
    }

    /**
     * 統計情報を追加する
     */
    public void addStatistic(String key, Object value) {
        this.statistics.put(key, value);
    }
}