package com.ms.bp.shared.common.config;

import com.ms.bp.shared.common.constants.BusinessConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 設定管理クラス
 * 環境に応じた設定ファイルから設定値を読み込む
 * 環境変数での上書きもサポート
 */
public class ConfigurationManager {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationManager.class);
    
    private static ConfigurationManager instance;
    private final Properties properties;

    /**
     * プライベートコンストラクタ（シングルトンパターン）
     */
    private ConfigurationManager() {
        this.properties = new Properties();
        loadConfiguration();
    }
    
    /**
     * シングルトンインスタンスを取得
     * @return ConfigurationManagerインスタンス
     */
    public static synchronized ConfigurationManager getInstance() {
        if (instance == null) {
            instance = new ConfigurationManager();
        }
        return instance;
    }
    
    /**
     * 設定ファイルを読み込む
     */
    private void loadConfiguration() {
        // アクティブプロファイルを取得
        String activeProfile = getActiveProfile();
        logger.info("アクティブプロファイル: {}", activeProfile);
        
        // デフォルト設定を読み込み
        loadPropertiesFile("application.properties");
        
        // 環境固有の設定を読み込み（存在する場合）
        if (!BusinessConstants.DEFAULT_PROFILE.equals(activeProfile)) {
            String profileSpecificFile = "application-" + activeProfile + ".properties";
            loadPropertiesFile(profileSpecificFile);
        }
        
        logger.info("設定ファイルの読み込みが完了しました。読み込まれた設定数: {}", properties.size());
    }
    
    /**
     * プロパティファイルを読み込む
     * @param fileName ファイル名
     */
    private void loadPropertiesFile(String fileName) {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName)) {
            if (inputStream != null) {
                properties.load(inputStream);
                logger.debug("設定ファイルを読み込みました: {}", fileName);
            } else {
                logger.warn("設定ファイルが見つかりません: {}", fileName);
            }
        } catch (IOException e) {
            logger.error("設定ファイルの読み込みエラー: {}", fileName, e);
        }
    }
    
    /**
     * アクティブプロファイルを取得
     * @return アクティブプロファイル
     */
    private String getActiveProfile() {
        // 環境変数から取得、なければデフォルト
        String profile = System.getenv(BusinessConstants.ENV_PROFILE);
        if (profile == null || profile.trim().isEmpty()) {
            profile = System.getProperty("spring.profiles.active", BusinessConstants.DEFAULT_PROFILE);
        }
        return profile.trim().toLowerCase();
    }
    
    /**
     * 設定値を取得（環境変数での上書きをサポート）
     * @param key 設定キー
     * @return 設定値
     */
    public String getProperty(String key) {
        return getProperty(key, null);
    }
    
    /**
     * 設定値を取得（デフォルト値付き、環境変数での上書きをサポート）
     * @param key 設定キー
     * @param defaultValue デフォルト値
     * @return 設定値
     */
    public String getProperty(String key, String defaultValue) {
        // 1. 環境変数から取得を試行（キーを大文字に変換し、ドットをアンダースコアに変換）
        String envKey = key.toUpperCase().replace('.', '_');
        String envValue = System.getenv(envKey);
        if (envValue != null && !envValue.trim().isEmpty()) {
            logger.debug("環境変数から設定値を取得: {} = {}", envKey, envValue);
            return envValue.trim();
        }
        
        // 2. プロパティファイルから取得
        String propValue = properties.getProperty(key);
        if (propValue != null && !propValue.trim().isEmpty()) {
            logger.debug("設定ファイルから設定値を取得: {} = {}", key, propValue);
            return propValue.trim();
        }
        
        // 3. デフォルト値を返す
        logger.debug("デフォルト値を使用: {} = {}", key, defaultValue);
        return defaultValue;
    }
    
    /**
     * 整数値の設定を取得
     * @param key 設定キー
     * @param defaultValue デフォルト値
     * @return 設定値
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                logger.warn("設定値が整数ではありません: {} = {}, デフォルト値を使用: {}", key, value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    /**
     * ブール値の設定を取得
     * @param key 設定キー
     * @param defaultValue デフォルト値
     * @return 設定値
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }
    
    /**
     * 必須設定値を取得（値が存在しない場合は例外をスロー）
     * @param key 設定キー
     * @return 設定値
     * @throws IllegalStateException 設定値が存在しない場合
     */
    public String getRequiredProperty(String key) {
        String value = getProperty(key);
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalStateException("必須設定が見つかりません: " + key);
        }
        return value;
    }
    
    /**
     * 設定値が存在するかチェック
     * @param key 設定キー
     * @return 存在する場合true
     */
    public boolean hasProperty(String key) {
        return getProperty(key) != null;
    }
    
    /**
     * 全ての設定をログ出力（デバッグ用）
     * 機密情報は出力しない
     */
    public void logConfiguration() {
        if (logger.isDebugEnabled()) {
            logger.debug("=== 設定情報 ===");
            properties.entrySet().stream()
                .filter(entry -> !isSensitiveKey(entry.getKey().toString()))
                .forEach(entry -> logger.debug("{} = {}", entry.getKey(), entry.getValue()));
            logger.debug("===============");
        }
    }
    
    /**
     * 機密情報のキーかどうかをチェック
     * @param key 設定キー
     * @return 機密情報の場合true
     */
    private boolean isSensitiveKey(String key) {
        String lowerKey = key.toLowerCase();
        return lowerKey.contains("password") || 
               lowerKey.contains("secret") || 
               lowerKey.contains("key") ||
               lowerKey.contains("token");
    }
}
