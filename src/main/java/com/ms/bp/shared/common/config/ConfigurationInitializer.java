package com.ms.bp.shared.common.config;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 設定初期化クラス
 * アプリケーション起動時に設定を初期化し、システムプロパティを設定する
 */
public class ConfigurationInitializer {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationInitializer.class);

    /**
     * -- GETTER --
     *  初期化状態を確認する
     *
     */
    @Getter
    private static boolean initialized = false;
    
    /**
     * 設定を初期化する
     * アプリケーション起動時に一度だけ実行される
     */
    public static synchronized void initialize() {
        if (initialized) {
            logger.debug("設定は既に初期化済みです");
            return;
        }
        
        try {
            logger.info("設定の初期化を開始します...");
            
            // ConfigurationManagerのインスタンスを取得（これにより設定ファイルが読み込まれる）
            ConfigurationManager config = ConfigurationManager.getInstance();
            
            // ログ設定をシステムプロパティに設定（log4j2.xmlで参照される）
            setSystemPropertyFromConfig(config, "aws.lambda.log.format");
            setSystemPropertyFromConfig(config, "aws.lambda.log.level");
            
            // その他の重要な設定をシステムプロパティに設定
            setSystemPropertyFromConfig(config, "aws.region");
            
            // 設定情報をログ出力（デバッグ用）
            config.logConfiguration();
            
            initialized = true;
            logger.info("設定の初期化が完了しました");
            
        } catch (Exception e) {
            logger.error("設定の初期化中にエラーが発生しました", e);
            throw new RuntimeException("設定の初期化に失敗しました", e);
        }
    }
    
    /**
     * 設定値をシステムプロパティに設定する
     * @param config 設定管理器
     * @param key 設定キー
     */
    private static void setSystemPropertyFromConfig(ConfigurationManager config, String key) {
        String value = config.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            System.setProperty(key, value);
            logger.debug("システムプロパティを設定: {} = {}", key, value);
        }
    }

    /**
     * 初期化状態をリセットする（テスト用）
     */
    public static synchronized void reset() {
        initialized = false;
        logger.debug("設定初期化状態をリセットしました");
    }
}
