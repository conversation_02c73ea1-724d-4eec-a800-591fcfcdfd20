package com.ms.bp.shared.common.io.validation.example;

import com.ms.bp.shared.common.io.validation.annotation.HalfWidthAlphanumeric;
import com.ms.bp.shared.common.io.validation.annotation.Required;
import com.ms.bp.shared.common.io.validation.AnnotationValidator;
import com.ms.bp.shared.common.exception.ValidationError;
import lombok.Data;

import java.util.List;

/**
 * HalfWidthAlphanumeric注解の使用例
 * 半角英数字校验器の動作を示すサンプルクラス
 */
public class HalfWidthAlphanumericExample {

    /**
     * 半角英数字校验のテスト用データクラス
     */
    @Data
    public static class UserData {
        
        /**
         * ユーザーID - 半角英数字のみ許可
         */
        @Required(fieldName = "ユーザーID")
        @HalfWidthAlphanumeric(fieldName = "ユーザーID")
        private String userId;
        
        /**
         * パスワード - 半角英数字のみ許可（カスタムエラーメッセージ）
         */
        @HalfWidthAlphanumeric(
            fieldName = "パスワード", 
            message = "パスワードは半角英数字のみで入力してください"
        )
        private String password;
        
        /**
         * 製品コード - 半角英数字のみ許可（カスタムエラーコード）
         */
        @HalfWidthAlphanumeric(
            fieldName = "製品コード",
            errorCode = "CUSTOM_001"
        )
        private String productCode;
    }

    /**
     * 半角英数字校验のテスト実行例
     */
    public static void main(String[] args) {
        // テストデータの作成
        UserData validData = new UserData();
        validData.setUserId("user123");           // 有効：半角英数字
        validData.setPassword("pass456");         // 有効：半角英数字
        validData.setProductCode("PROD789");      // 有効：半角英数字

        UserData invalidData = new UserData();
        invalidData.setUserId("ユーザー123");      // 無効：全角文字を含む
        invalidData.setPassword("pass@456");      // 無効：特殊文字を含む
        invalidData.setProductCode("PROD 789");   // 無効：空白を含む

        // 有効データの検証
        System.out.println("=== 有効データの検証 ===");
        List<ValidationError> validErrors = AnnotationValidator.validate(validData);
        if (validErrors.isEmpty()) {
            System.out.println("検証成功：エラーなし");
        } else {
            validErrors.forEach(error -> System.out.println("エラー: " + error.getMessage()));
        }

        // 無効データの検証
        System.out.println("\n=== 無効データの検証 ===");
        List<ValidationError> invalidErrors = AnnotationValidator.validate(invalidData);
        if (invalidErrors.isEmpty()) {
            System.out.println("検証成功：エラーなし");
        } else {
            invalidErrors.forEach(error -> System.out.println("エラー: " + error.getMessage()));
        }
    }
}
