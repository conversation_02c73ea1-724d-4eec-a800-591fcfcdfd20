package com.ms.bp.shared.common.io.validation;

import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.Message;
import com.ms.bp.shared.util.MessageFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * エラーメッセージ解析器
 * GlobalCodeConstantsとの統合により、すべてのエラーコードを統一的に処理
 */
public class ErrorMessageResolver {

    private static final Logger logger = LoggerFactory.getLogger(ErrorMessageResolver.class);

    /** エラーコードとMessageオブジェクトのマッピング（スレッドセーフ） */
    private static final Map<String, Message> ERROR_CODE_MAP = new ConcurrentHashMap<>();

    /** デフォルトメッセージテンプレート（検証タイプ別） */
    private static final Map<ValidationType, String> DEFAULT_TEMPLATES = Map.of(
        ValidationType.REQUIRED, "項目：{0}、エラー内容：必須チェックエラー",
        ValidationType.RANGE, "項目：{0}、エラー内容：桁数エラー。{1}桁から{2}桁の間で入力してください。",
        ValidationType.NUMERIC_HALF_WIDTH, "項目：{0}、エラー内容：書式エラー。半角数字で入力してください。",
        ValidationType.HALF_WIDTH_ALPHANUMERIC, "項目：{0}、エラー内容：書式エラー。半角英数で入力してください。"
    );

    /** 検証タイプの列挙型 */
    public enum ValidationType {
        REQUIRED, RANGE, NUMERIC_HALF_WIDTH, HALF_WIDTH_ALPHANUMERIC
    }

    static {
        // 静的初期化でGlobalCodeConstantsからすべてのMessageフィールドを読み込み
        loadErrorCodes();
    }

    /**
     * エラーメッセージを解析して生成
     * 優先順位：カスタムメッセージ > GlobalCodeConstants > デフォルトテンプレート
     *
     * @param errorCode エラーコード
     * @param customMessage カスタムメッセージ
     * @param validationType 検証タイプ
     * @param displayName 表示用フィールド名
     * @param parameters 追加パラメータ
     * @return 生成されたエラーメッセージ
     */
    public static String resolveMessage(String errorCode, String customMessage, 
                                      ValidationType validationType, String displayName, 
                                      Object... parameters) {
        // カスタムメッセージが指定されている場合
        if (customMessage != null && !customMessage.trim().isEmpty()) {
            return formatCustomMessage(customMessage, displayName, parameters);
        }
        
        // エラーコードが指定されている場合、GlobalCodeConstantsから取得
        if (errorCode != null && !errorCode.trim().isEmpty()) {
            Message message = ERROR_CODE_MAP.get(errorCode);
            if (message != null) {
                return MessageFormatter.format(message.getMsg(), 
                    combineParameters(displayName, parameters));
            }
        }
        
        // デフォルトテンプレートを使用
        String defaultTemplate = DEFAULT_TEMPLATES.get(validationType);
        if (defaultTemplate != null) {
            return MessageFormatter.format(defaultTemplate, 
                combineParameters(displayName, parameters));
        }
        
        // 最終的なフォールバック
        return displayName + "の検証エラーが発生しました";
    }

    /**
     * GlobalCodeConstantsからすべてのMessageフィールドを読み込み
     */
    private static void loadErrorCodes() {
        try {
            Field[] fields = GlobalMessageConstants.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.getType() == Message.class && 
                    Modifier.isStatic(field.getModifiers()) && 
                    Modifier.isFinal(field.getModifiers())) {
                    
                    field.setAccessible(true);
                    Message message = (Message) field.get(null);
                    ERROR_CODE_MAP.put(field.getName(), message);
                }
            }
            logger.info("エラーコードマッピングを読み込み完了: {}個のエラーコード", ERROR_CODE_MAP.size());
        } catch (Exception e) {
            logger.warn("GlobalCodeConstantsの読み込み中にエラーが発生しました", e);
        }
    }

    /**
     * カスタムメッセージをフォーマット
     */
    private static String formatCustomMessage(String customMessage, String displayName, Object... parameters) {
        String formatted = customMessage.replace("{field}", displayName);
        
        // 追加パラメータがある場合の処理
        if (parameters != null && parameters.length > 0) {
            for (int i = 0; i < parameters.length; i++) {
                formatted = formatted.replace("{" + (i + 1) + "}", String.valueOf(parameters[i]));
            }
        }
        
        return formatted;
    }

    /**
     * 表示名と追加パラメータを結合
     */
    private static Object[] combineParameters(String displayName, Object... parameters) {
        if (parameters == null || parameters.length == 0) {
            return new Object[]{displayName};
        }
        
        Object[] combined = new Object[parameters.length + 1];
        combined[0] = displayName;
        System.arraycopy(parameters, 0, combined, 1, parameters.length);
        return combined;
    }

}
