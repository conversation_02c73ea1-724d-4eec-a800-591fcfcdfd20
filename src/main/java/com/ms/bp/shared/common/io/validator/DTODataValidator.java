package com.ms.bp.shared.common.io.validator;

import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.mapper.DTOMapper;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validation.AnnotationValidator;

import java.util.List;
import java.util.Map;

/**
 * DTOベースのデータバリデーター
 * Map -> DTO -> アノテーション検証の流れを実装
 * 
 * @param <T> DTOの型
 */
public class DTODataValidator<T> implements DataValidator {
    
    private final Class<T> dtoClass;
    private final DataValidator customValidator;
    
    /**
     * コンストラクタ（アノテーション検証のみ）
     * 
     * @param dtoClass DTOクラス
     */
    public DTODataValidator(Class<T> dtoClass) {
        this(dtoClass, null);
    }
    
    /**
     * コンストラクタ（アノテーション検証 + カスタム検証）
     * 
     * @param dtoClass DTOクラス
     * @param customValidator カスタムバリデーター（nullの場合はアノテーション検証のみ）
     */
    public DTODataValidator(Class<T> dtoClass, DataValidator customValidator) {
        this.dtoClass = dtoClass;
        this.customValidator = customValidator;
    }
    
    @Override
    public List<ValidationError> validate(Map<String, Object> data, ImportOptions options) {
        try {
            // Step 1: Map を DTO にマッピング
            T dto = DTOMapper.mapToDTO(data, dtoClass);
            
            // Step 2: アノテーションベースの検証を実行
            List<ValidationError> errors = AnnotationValidator.validate(dto);
            
            // Step 3: カスタム検証があれば実行
            if (customValidator != null) {
                List<ValidationError> customErrors = customValidator.validate(data, options);
                errors.addAll(customErrors);
            }
            
            return errors;
            
        } catch (Exception e) {
            // マッピングエラーの場合
            ValidationError error = new ValidationError("mapping", data, 
                    "データマッピングエラー: " + e.getMessage());
            return List.of(error);
        }
    }
}
