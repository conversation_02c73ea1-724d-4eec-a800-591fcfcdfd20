package com.ms.bp.shared.common.io.mapper;

import lombok.Getter;

import java.lang.reflect.Field;

/**
 * フィールドメタデータクラス
 * DTOマッピング時のパフォーマンス最適化のため、フィールド情報をキャッシュします
 */
@Getter
public class FieldMetadata {
    
    /** リフレクションフィールドオブジェクト
     * -- GETTER --
     *  リフレクションフィールドオブジェクトを取得
     *
     */
    private final Field field;
    
    /** スネークケース形式のフィールド名（マップキー用）
     * -- GETTER --
     *  スネークケース形式のフィールド名を取得
     *
     */
    private final String snakeCaseName;
    
    /** フィールドの型
     * -- GETTER --
     *  フィールドの型を取得
     *
     */
    private final Class<?> fieldType;
    
    /**
     * コンストラクタ
     * 
     * @param field リフレクションフィールドオブジェクト
     * @param snakeCaseName スネークケース形式のフィールド名
     * @param fieldType フィールドの型
     */
    public FieldMetadata(Field field, String snakeCaseName, Class<?> fieldType) {
        this.field = field;
        this.snakeCaseName = snakeCaseName;
        this.fieldType = fieldType;
    }

    /**
     * フィールド名を取得（デバッグ用）
     * 
     * @return フィールド名
     */
    public String getFieldName() {
        return field.getName();
    }
    
    @Override
    public String toString() {
        return "FieldMetadata{" +
                "fieldName='" + field.getName() + '\'' +
                ", snakeCaseName='" + snakeCaseName + '\'' +
                ", fieldType=" + fieldType.getSimpleName() +
                '}';
    }
}
