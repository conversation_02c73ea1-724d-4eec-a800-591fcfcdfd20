package com.ms.bp.shared.common.io.strategy;


import com.ms.bp.shared.common.io.options.ImportOptions;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * ファイル解析戦略インターフェース
 * （戦略パターン：異なるファイル形式に対して異なる解析戦略を定義）
 */
public interface FileParsingStrategy {
    /**
     * ファイルを解析する
     *
     * @param inputStream 入力ストリーム
     * @param options インポートオプション
     * @return 解析されたデータのリスト
     * @throws IOException 入出力例外
     */
    List<Map<String, Object>> parseFile(InputStream inputStream, ImportOptions options) throws IOException;
}