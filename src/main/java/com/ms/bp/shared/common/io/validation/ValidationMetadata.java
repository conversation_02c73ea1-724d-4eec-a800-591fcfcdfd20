package com.ms.bp.shared.common.io.validation;

import com.ms.bp.shared.common.io.validation.annotation.Required;
import com.ms.bp.shared.common.io.validation.annotation.Range;
import com.ms.bp.shared.common.io.validation.annotation.NumericHalfWidth;
import com.ms.bp.shared.common.io.validation.annotation.HalfWidthAlphanumeric;
import lombok.Getter;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * バリデーションメタデータクラス
 * アノテーションベースの検証時のパフォーマンス最適化のため、フィールドの検証情報をキャッシュします
 */
@Getter
public class ValidationMetadata {
    
    /** リフレクションフィールドオブジェクト
     * -- GETTER --
     *  リフレクションフィールドオブジェクトを取得
     *
     */
    private final Field field;
    
    /**
     フィールド名
     */
    private final String fieldName;
    
    /**
     * 検証ルールのリスト
     */
    private final List<ValidationRule> validationRules;
    
    /**
     * コンストラクタ
     * フィールドのアノテーションを解析して検証ルールを構築します
     * 
     * @param field リフレクションフィールドオブジェクト
     */
    public ValidationMetadata(Field field) {
        this.field = field;
        this.fieldName = field.getName();
        this.validationRules = new ArrayList<>();
        
        // フィールドのアクセス権限を設定（一度だけ）
        field.setAccessible(true);
        
        // アノテーションを解析して検証ルールを構築
        buildValidationRules();
    }
    
    /**
     * フィールドのアノテーションを解析して検証ルールを構築
     */
    private void buildValidationRules() {
        // 必須チェック
        if (field.isAnnotationPresent(Required.class)) {
            Required annotation = field.getAnnotation(Required.class);
            validationRules.add(new ValidationRule(ValidationRule.Type.REQUIRED,
                annotation.fieldName(), annotation.errorCode(), annotation.message(), null));
        }

        // 範囲チェック
        if (field.isAnnotationPresent(Range.class)) {
            Range annotation = field.getAnnotation(Range.class);
            validationRules.add(new ValidationRule(ValidationRule.Type.RANGE,
                annotation.fieldName(), annotation.errorCode(), annotation.message(),
                new long[]{annotation.min(), annotation.max()}));
        }

        // 半角数字チェック
        if (field.isAnnotationPresent(NumericHalfWidth.class)) {
            NumericHalfWidth annotation = field.getAnnotation(NumericHalfWidth.class);
            validationRules.add(new ValidationRule(ValidationRule.Type.NUMERIC_HALF_WIDTH,
                annotation.fieldName(), annotation.errorCode(), annotation.message(), null));
        }

        // 半角英数字チェック
        if (field.isAnnotationPresent(HalfWidthAlphanumeric.class)) {
            HalfWidthAlphanumeric annotation = field.getAnnotation(HalfWidthAlphanumeric.class);
            validationRules.add(new ValidationRule(ValidationRule.Type.HALF_WIDTH_ALPHANUMERIC,
                annotation.fieldName(), annotation.errorCode(), annotation.message(), null));
        }
    }


    @Override
    public String toString() {
        return "ValidationMetadata{" +
                "fieldName='" + fieldName + '\'' +
                ", rulesCount=" + validationRules.size() +
                '}';
    }
}
