package com.ms.bp.shared.common.io.strategy;


import com.ms.bp.shared.common.io.options.ExportOptions;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * データ書き込み戦略インターフェース
 * （戦略パターン：異なる出力形式に対して異なる書き込み戦略を定義）
 */
public interface DataWriteStrategy<T> {
    /**
     * ヘッダーを書き込む
     *
     * @param columns 列名のリスト
     * @param outputStream 出力ストリーム
     * @param options エクスポートオプション
     * @throws IOException 入出力例外
     */
    void writeHeader(List<String> columns, OutputStream outputStream, ExportOptions options) throws IOException;

    /**
     * レコードを書き込む
     *
     * @param data データマップ
     * @param columns 列名のリスト
     * @param outputStream 出力ストリーム
     * @param options エクスポートオプション
     * @throws IOException 入出力例外
     */
    void writeRecord(Map<String, Object> data, List<String> columns, OutputStream outputStream, ExportOptions options) throws IOException;

    /**
     * フッターを書き込む
     *
     * @param outputStream 出力ストリーム
     * @param options エクスポートオプション
     * @throws IOException 入出力例外
     */
    void writeFooter(OutputStream outputStream, ExportOptions options) throws IOException;

    /**
     * 書き込みを完了する
     *
     * @param outputStream 出力ストリーム
     * @param options エクスポートオプション
     * @throws IOException 入出力例外
     */
    void finish(OutputStream outputStream, ExportOptions options) throws IOException;
}
