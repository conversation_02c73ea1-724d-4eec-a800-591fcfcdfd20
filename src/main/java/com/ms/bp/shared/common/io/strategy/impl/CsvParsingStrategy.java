package com.ms.bp.shared.common.io.strategy.impl;

import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.strategy.FileParsingStrategy;
import com.opencsv.CSVReader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * CSV解析戦略実装
 */
public class CsvParsingStrategy implements FileParsingStrategy {
    private static final Logger logger = LoggerFactory.getLogger(CsvParsingStrategy.class);

    @Override
    public List<Map<String, Object>> parseFile(InputStream inputStream, ImportOptions options) throws IOException {
        List<Map<String, Object>> result = new ArrayList<>();

        try (Reader reader = new InputStreamReader(inputStream);
             CSVReader csvReader = new CSVReader(reader)) {

            String[] header = null;
            String[] mappedHeader = null;
            if (options.isHasHeader()) {
                header = csvReader.readNext();
                if (header != null) {
                    // ヘッダー行を処理（空白を除去）
                    for (int i = 0; i < header.length; i++) {
                        header[i] = header[i].trim();
                    }

                    // CSVヘッダー検証を実行
                    validateCsvHeader(header, options);

                    // フィールドマッピングが有効な場合、ヘッダーを変換
                    if (options.isEnableFieldMapping() && options.getHeaderFieldMapping() != null) {
                        mappedHeader = new String[header.length];
                        for (int i = 0; i < header.length; i++) {
                            String mappedField = options.getHeaderFieldMapping().get(header[i]);
                            mappedHeader[i] = mappedField != null ? mappedField : header[i];
                            logger.debug("ヘッダーマッピング: {} -> {}", header[i], mappedHeader[i]);
                        }
                    } else {
                        mappedHeader = header;
                    }
                }
            }

            String[] line;
            int rowCount = 0;

            while ((line = csvReader.readNext()) != null) {
                rowCount++;

                try {
                    Map<String, Object> data = new HashMap<>();

                    if (mappedHeader != null) {
                        // マッピング済みヘッダーを使用してデータをマッピング
                        for (int i = 0; i < Math.min(mappedHeader.length, line.length); i++) {
                            data.put(mappedHeader[i], line[i].trim());
                        }
                    } else {
                        // ヘッダー行がない場合、列インデックスをキーとして使用
                        for (int i = 0; i < line.length; i++) {
                            data.put("column" + i, line[i].trim());
                        }
                    }

                    result.add(data);
                } catch (Exception e) {
                    logger.error("行 {}の解析中にエラーが発生しました: {}", rowCount, e.getMessage());
                    // エラー時に処理を続行するかどうか
                    if (!options.isContinueOnError()) {
                        throw new IOException("行 " + rowCount + " の解析に失敗しました: " + e.getMessage(), e);
                    }
                }
            }

            logger.info("CSVファイルから{}行を解析しました", rowCount);
            return result;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            if (e instanceof IOException) {
                throw (IOException) e;
            }
            throw new IOException("CSVファイル解析エラー: " + e.getMessage(), e);
        }
    }

    /**
     * CSVヘッダーの検証を実行
     * ImportOptionsのheaderFieldMappingと実際のCSVヘッダーを比較し、
     * フィールド数と各フィールド名が一致することを確認する
     *
     * @param actualHeader 実際のCSVヘッダー配列
     * @param options インポートオプション
     * @throws ServiceException ヘッダー検証に失敗した場合
     */
    private void validateCsvHeader(String[] actualHeader, ImportOptions options) {
        // フィールドマッピングが無効またはheaderFieldMappingがnullの場合は検証をスキップ
        if (!options.isEnableFieldMapping() || options.getHeaderFieldMapping() == null) {
            logger.debug("ヘッダー検証をスキップします（フィールドマッピングが無効またはheaderFieldMappingがnull）");
            return;
        }

        Set<String> expectedHeaders = options.getHeaderFieldMapping().keySet();
        Set<String> actualHeaderSet = new HashSet<>(Arrays.asList(actualHeader));

        // ヘッダーが完全に一致するかチェック
        if (!expectedHeaders.equals(actualHeaderSet)) {
            logger.error("CSVヘッダーが一致しません - 期待値: {}, 実際: {}", expectedHeaders, actualHeaderSet);
            throw new ServiceException(GlobalMessageConstants.ERR_010);
        }

    }
}