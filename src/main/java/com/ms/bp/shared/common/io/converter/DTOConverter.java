package com.ms.bp.shared.common.io.converter;

import com.ms.bp.shared.common.io.options.ImportOptions;
import java.util.Map;

/**
 * DTOからデータベースフィールドへの変換ユーティリティクラス
 * パフォーマンス最適化：DatabaseMappableインターフェースを使用し、反射を完全回避
 *
 * 注意：このクラスは継承不要の工具类です。静的メソッドを直接使用してください。
 */
public final class DTOConverter {

    // インスタンス化を防ぐ
    private DTOConverter() {
        throw new UnsupportedOperationException("ユーティリティクラスはインスタンス化できません");
    }

    /**
     * DTOをデータベースフィールドのMapに変換します
     * 固定値フィールドの注入をサポート
     * パフォーマンス最適化：インターフェースメソッドを直接呼び出し、反射を完全回避
     *
     * @param dto DTOオブジェクト（DatabaseMappableを実装している必要があります）
     * @param isInsert 挿入操作であるかどうか
     * @param options インポートオプション（固定値フィールドを含む）
     * @return データベースフィールドのMap
     * @param <T> DatabaseMappableを実装するDTOのタイプ
     */
    public static <T extends DatabaseMappable> Map<String, Object> toDatabase(T dto, boolean isInsert, ImportOptions options) {
        if (dto == null) {
            throw new IllegalArgumentException("DTOがnullです");
        }
        return dto.toDatabaseFields(isInsert, options); // 直接メソッド呼び出し、反射なし
    }
}

