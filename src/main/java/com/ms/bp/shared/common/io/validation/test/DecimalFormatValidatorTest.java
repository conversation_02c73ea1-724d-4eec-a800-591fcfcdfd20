package com.ms.bp.shared.common.io.validation.test;

import com.ms.bp.shared.common.io.validation.annotation.DecimalFormat;
import com.ms.bp.shared.common.io.validation.AnnotationValidator;
import com.ms.bp.shared.common.exception.ValidationError;
import lombok.Data;

import java.util.List;

/**
 * DecimalFormat校验器のテストクラス
 * 数値フォーマット校验の動作確認用
 */
public class DecimalFormatValidatorTest {

    @Data
    public static class TestData {
        @DecimalFormat(fieldName = "テスト値")
        private String value;
    }

    /**
     * 校验テストの実行
     */
    public static void main(String[] args) {
        System.out.println("=== DecimalFormat校验器テスト ===\n");

        // 有効値テスト
        testValidValues();
        
        // 無効値テスト
        testInvalidValues();
        
        // null値テスト
        testNullValue();
    }

    /**
     * 有効値のテスト
     */
    private static void testValidValues() {
        System.out.println("--- 有効値テスト ---");
        String[] validValues = {
            "1.00",      // 最小値
            "1.01",      // 最小値+0.01
            "123.45",    // 中間値
            "999.98",    // 最大値-0.01
            "999.99"     // 最大値
        };

        for (String value : validValues) {
            boolean isValid = validateValue(value);
            System.out.println(value + " -> " + (isValid ? "✓ 有効" : "✗ 無効"));
        }
        System.out.println();
    }

    /**
     * 無効値のテスト
     */
    private static void testInvalidValues() {
        System.out.println("--- 無効値テスト ---");
        String[] invalidValues = {
            "0.99",      // 最小値未満
            "1000.00",   // 最大値超過
            "1.1",       // 小数部分1桁
            "1.123",     // 小数部分3桁
            "1",         // 小数点なし
            "abc.12",    // 非数値文字
            "12.ab",     // 小数部分に非数値
            "-1.00",     // 負数
            "01.00",     // 先頭0
            "1.00.00",   // 小数点複数
            " 1.00",     // 先頭空白
            "1.00 ",     // 末尾空白
            "",          // 空文字列
            ".",         // 小数点のみ
            "1.",        // 小数部分なし
            ".00"        // 整数部分なし
        };

        for (String value : invalidValues) {
            boolean isValid = validateValue(value);
            System.out.println(value + " -> " + (isValid ? "✗ 予期しない有効" : "✓ 無効"));
        }
        System.out.println();
    }

    /**
     * null値のテスト（null値は校验をスキップするべき）
     */
    private static void testNullValue() {
        System.out.println("--- null値テスト ---");
        TestData data = new TestData();
        data.setValue(null);
        
        List<ValidationError> errors = AnnotationValidator.validate(data);
        boolean isValid = errors.isEmpty();
        System.out.println("null -> " + (isValid ? "✓ スキップ（正常）" : "✗ エラー（異常）"));
        System.out.println();
    }

    /**
     * 値を校验してエラーの有無を返す
     */
    private static boolean validateValue(String value) {
        TestData data = new TestData();
        data.setValue(value);
        
        List<ValidationError> errors = AnnotationValidator.validate(data);
        
        // エラーがある場合は詳細を表示
        if (!errors.isEmpty()) {
            for (ValidationError error : errors) {
                System.out.println("    エラー: " + error.getMessage());
            }
        }
        
        return errors.isEmpty();
    }
}
