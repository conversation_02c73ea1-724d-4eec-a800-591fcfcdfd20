package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;

/**
 * 組織エリアマスタデータアクセス実装
 * M_SOSHIKIAREAMST（組織エリアマスタ）への具体的なデータアクセスを実装
 */
public class GroupAreaDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(GroupAreaDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_AREA_CODE_SQL = """
        SELECT 1
        FROM M_SOSHIKIAREAMST
        WHERE AREA_CODE = ?
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public GroupAreaDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * エリアコードで組織エリアマスタの存在チェック
     *
     * @param areaCode エリアコード（4桁）
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByAreaCode(String areaCode) throws SQLException {
        logger.debug("組織エリアマスタ存在チェック実行: エリアコード={}", areaCode);

        Object[] params = {areaCode};

        var results = jdbcTemplate.query(EXISTS_BY_AREA_CODE_SQL, params, rs -> rs.getInt(1));

        boolean exists = !results.isEmpty();
        logger.debug("組織エリアマスタ存在チェック結果: エリアコード={}, 存在={}", areaCode, exists);

        return exists;
    }


}
