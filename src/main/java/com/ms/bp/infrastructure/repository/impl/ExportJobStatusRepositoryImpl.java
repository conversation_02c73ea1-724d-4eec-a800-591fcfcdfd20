package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.file.repository.ExportJobStatusRepository;
import com.ms.bp.infrastructure.repository.dao.ExportJobStatusDataAccess;
import com.ms.bp.domain.file.model.ExportJobStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * エクスポートジョブステータスリポジトリ実装
 */
public class ExportJobStatusRepositoryImpl implements ExportJobStatusRepository {
    private static final Logger logger = LoggerFactory.getLogger(ExportJobStatusRepositoryImpl.class);
    
    private final ExportJobStatusDataAccess dataAccess;

    public ExportJobStatusRepositoryImpl(ExportJobStatusDataAccess dataAccess) {
        this.dataAccess = dataAccess;
    }
    
    @Override
    public void save(ExportJobStatus jobStatus) {
        try {
            // データベースに挿入し、自動生成された履歴番号を取得
            Long generatedRrkBango = dataAccess.insert(jobStatus);

            // 生成された履歴番号をエンティティに設定
            jobStatus.setRrkBango(generatedRrkBango);

            logger.debug("エクスポートジョブステータスを保存しました: rrkBango={}", generatedRrkBango);
        } catch (SQLException e) {
            logger.error("エクスポートジョブステータス保存中にエラーが発生しました: rrkBango={}", jobStatus.getRrkBango(), e);
            throw new RuntimeException("エクスポートジョブステータス保存に失敗しました", e);
        }
    }

    @Override
    public void update(ExportJobStatus jobStatus) {
        try {
            dataAccess.update(jobStatus);
            logger.debug("エクスポートジョブステータスを更新しました: rrkBango={}", jobStatus.getRrkBango());
        } catch (SQLException e) {
            logger.error("エクスポートジョブステータス更新中にエラーが発生しました: rrkBango={}", jobStatus.getRrkBango(), e);
            throw new RuntimeException("エクスポートジョブステータス更新に失敗しました", e);
        }
    }
    
    @Override
    public ExportJobStatus findByRrkBango(Long rrkBango) {
        try {
            ExportJobStatus jobStatus = dataAccess.findByRrkBango(rrkBango);
            logger.debug("エクスポートジョブステータスを取得しました: rrkBango={}", rrkBango);
            return jobStatus;
        } catch (SQLException e) {
            logger.error("エクスポートジョブステータス取得中にエラーが発生しました: rrkBango={}", rrkBango, e);
            throw new RuntimeException("エクスポートジョブステータス取得に失敗しました", e);
        }
    }

    @Override
    public List<ExportJobStatus> findByShainCode(String shainCode,String systemOperationCompanyCode, int limit, int offset) {
        try {
            List<ExportJobStatus> jobStatusList = dataAccess.findByShainCode(shainCode, systemOperationCompanyCode, limit, offset);
            logger.debug("エクスポートジョブステータスリスト（エリア名変換済み）を取得しました: shainCode={}, count={}", shainCode, jobStatusList.size());
            return jobStatusList;
        } catch (SQLException e) {
            logger.error("エクスポートジョブステータスリスト取得中にエラーが発生しました: shainCode={}", shainCode, e);
            throw new RuntimeException("エクスポートジョブステータスリスト取得に失敗しました", e);
        }
    }
}
