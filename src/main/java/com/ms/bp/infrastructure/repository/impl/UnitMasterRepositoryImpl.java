package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.repository.UnitMasterRepository;
import com.ms.bp.infrastructure.repository.dao.UnitMasterDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;

/**
 * ユーザーマスタリポジトリ実装
 * M_UNITMST（ユニットマスタ）へのデータアクセスを実装
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public class UnitMasterRepositoryImpl implements UnitMasterRepository {
    private static final Logger logger = LoggerFactory.getLogger(UnitMasterRepositoryImpl.class);

    private final UnitMasterDataAccess dataAccess;

    public UnitMasterRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new UnitMasterDataAccess(jdbcTemplate);
    }

    @Override
    public boolean existsByUnitCode(String unitCode) {
        try {
            boolean exists = dataAccess.existsByUnitCode(unitCode);
            logger.debug("ユニットマスタ存在チェックを実行しました: ユニットコード={}, 存在={}", unitCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("ユニットマスタ存在チェック中にエラーが発生しました: ユニットコード={}", unitCode, e);
            throw new RuntimeException("ユニットマスタ存在チェックに失敗しました", e);
        }
    }
}
