package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.repository.GroupAreaRepository;
import com.ms.bp.infrastructure.repository.dao.GroupAreaDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;

/**
 * 組織エリアマスタリポジトリ実装
 * M_SOSHIKIAREAMST（組織エリアマスタ）へのデータアクセスを実装
 */
public class GroupAreaRepositoryImpl implements GroupAreaRepository {
    private static final Logger logger = LoggerFactory.getLogger(GroupAreaRepositoryImpl.class);

    private final GroupAreaDataAccess dataAccess;

    public GroupAreaRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new GroupAreaDataAccess(jdbcTemplate);
    }
    
    @Override
    public boolean existsByAreaCode(String areaCode) {
        try {
            boolean exists = dataAccess.existsByAreaCode(areaCode);
            logger.debug("組織エリアマスタ存在チェックを実行しました: エリアコード={}, 存在={}", areaCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("組織エリアマスタ存在チェック中にエラーが発生しました: エリアコード={}", areaCode, e);
            throw new RuntimeException("組織エリアマスタ存在チェックに失敗しました", e);
        }
    }

}
