package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * ユニットマスタデータアクセス実装
 * M_UNITMST（ユニットマスタ）への具体的なデータアクセスを実装
 */
public class UnitMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(UnitMasterDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_UNIT_CODE_SQL = """
        SELECT 1
        FROM M_UNITMST
        WHERE UNIT_CODE = ?
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public UnitMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * ユニットコードでユニットマスタの存在チェック
     *
     * @param unitCode ユニットコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByUnitCode(String unitCode) throws SQLException {
        logger.debug("ユニットマスタ存在チェック開始: ユニットコード={}", unitCode);

        List<Integer> results = jdbcTemplate.query(
            EXISTS_BY_UNIT_CODE_SQL,
            new Object[]{unitCode},
            rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("ユニットマスタ存在チェック完了: ユニットコード={}, 存在={}", unitCode, exists);

        return exists;
    }
}
