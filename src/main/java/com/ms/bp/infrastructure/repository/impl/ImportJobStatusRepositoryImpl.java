package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.file.repository.ImportJobStatusRepository;
import com.ms.bp.infrastructure.repository.dao.ImportJobStatusDataAccess;
import com.ms.bp.domain.file.model.ImportJobStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * インポートジョブステータスリポジトリ実装
 */
public class ImportJobStatusRepositoryImpl implements ImportJobStatusRepository {
    private static final Logger logger = LoggerFactory.getLogger(ImportJobStatusRepositoryImpl.class);
    
    private final ImportJobStatusDataAccess dataAccess;

    public ImportJobStatusRepositoryImpl(ImportJobStatusDataAccess dataAccess) {
        this.dataAccess = dataAccess;
    }
    
    @Override
    public void save(ImportJobStatus jobStatus) {
        try {
            // データベースに挿入し、自動生成された履歴番号を取得
            Long generatedRrkBango = dataAccess.insert(jobStatus);

            // 生成された履歴番号をエンティティに設定
            jobStatus.setRrkBango(generatedRrkBango);

            logger.debug("インポートジョブステータスを保存しました: rrkBango={}", generatedRrkBango);
        } catch (SQLException e) {
            logger.error("インポートジョブステータス保存中にエラーが発生しました: rrkBango={}", jobStatus.getRrkBango(), e);
            throw new RuntimeException("インポートジョブステータス保存に失敗しました", e);
        }
    }

    @Override
    public void update(ImportJobStatus jobStatus) {
        try {
            dataAccess.update(jobStatus);
            logger.debug("インポートジョブステータスを更新しました: rrkBango={}", jobStatus.getRrkBango());
        } catch (SQLException e) {
            logger.error("インポートジョブステータス更新中にエラーが発生しました: rrkBango={}", jobStatus.getRrkBango(), e);
            throw new RuntimeException("インポートジョブステータス更新に失敗しました", e);
        }
    }
    
    @Override
    public ImportJobStatus findByRrkBango(Long rrkBango) {
        try {
            ImportJobStatus jobStatus = dataAccess.findByRrkBango(rrkBango);
            logger.debug("インポートジョブステータスを取得しました: rrkBango={}", rrkBango);
            return jobStatus;
        } catch (SQLException e) {
            logger.error("インポートジョブステータス取得中にエラーが発生しました: rrkBango={}", rrkBango, e);
            throw new RuntimeException("インポートジョブステータス取得に失敗しました", e);
        }
    }

    @Override
    public List<ImportJobStatus> findByShainCode(String shainCode, String systemOperationCompanyCode,int limit, int offset) {
        try {
            List<ImportJobStatus> jobStatusList = dataAccess.findByShainCode(shainCode, systemOperationCompanyCode,limit, offset);
            logger.debug("インポートジョブステータスリストを取得しました: shainCode={}, count={}", shainCode, jobStatusList.size());
            return jobStatusList;
        } catch (SQLException e) {
            logger.error("インポートジョブステータスリスト取得中にエラーが発生しました: shainCode={}", shainCode, e);
            throw new RuntimeException("インポートジョブステータスリスト取得に失敗しました", e);
        }
    }
}
