package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.file.model.AnnounceMessage;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.sql.SQLException;
import java.util.List;

/**
 * アナウンスメッセージアクセス実装
 */
public class AnnounceMessageDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(AnnounceMessageDataAccess.class);

    private final JdbcTemplate jdbcTemplate;

    /**
     * コンストラクタ構築
     *
     * @param jdbcTemplate DBツール
     */
    public AnnounceMessageDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * メッセージ区分でアナウンスメッセージを取得
     *
     * @param msgkbn メッセージ区分
     * @return アナウンスメッセージ
     */
    public  List<AnnounceMessage> findByMsgKubun(String msgkbn) throws SQLException {
        String sql = """
                    SELECT
                        mssg_kubun,
                        mssg
                    FROM
                        t_announcement_mssg
                    WHERE
                        mssg_kubun = ?
                        AND CURRENT_DATE::CHAR(8) >= kshb
                        AND CURRENT_DATE::CHAR(8) <= shryb
                    ORDER BY
                        rcrd_trk_nchj desc
                    ;
                """;

        List<AnnounceMessage> announceMessages = jdbcTemplate.query(sql, new Object[]{msgkbn}, rs -> {
            String msgKbn = rs.getString("mssg_kubun");
            String msg = rs.getString("mssg");
            return new AnnounceMessage(msgKbn, msg);
        });
        logger.debug("アナウンスメッセージ情報取得結果: メッセージ区分={},件数={}", msgkbn,announceMessages.size());
        return announceMessages;
    }
}