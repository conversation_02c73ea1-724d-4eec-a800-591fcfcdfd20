package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.file.model.AnnounceMessage;
import com.ms.bp.domain.master.repository.AnnounceMessageRepository;
import com.ms.bp.infrastructure.repository.dao.AnnounceMessageDataAccess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.sql.SQLException;
import java.util.List;

/**
 * アナウンスメッセージリポジトリ実装
 */
public class AnnounceMessageRepositoryImpl implements AnnounceMessageRepository {
    private static final Logger logger = LoggerFactory.getLogger(AnnounceMessageRepositoryImpl.class);

    private final AnnounceMessageDataAccess dataAccess;

    /**
     * コンストラクタ構築
     *
     * @param dataAccess DBアクセスツール
     */
    public AnnounceMessageRepositoryImpl(AnnounceMessageDataAccess dataAccess) {
        this.dataAccess = dataAccess;
    }

    /**
     * メッセージ区分でアナウンスメッセージを取得
     * 
     * @param msgkbn メッセージ区分
     * @return アナウンスメッセージ
     */
    @Override
    public List<AnnounceMessage> findByMsgKubun(String msgkbn){
        try {
            List<AnnounceMessage> announceMessages = dataAccess.findByMsgKubun(msgkbn);
            logger.debug("アナウンスメッセージを取得しました: msgkbn={}", msgkbn);
            return announceMessages;
        } catch (SQLException e) {
            logger.error("アナウンスメッセージ得中にエラーが発生しました: msgkbn={}", msgkbn, e);
            throw new RuntimeException("アナウンスメッセージ取得に失敗しました", e);
        }
    }
}