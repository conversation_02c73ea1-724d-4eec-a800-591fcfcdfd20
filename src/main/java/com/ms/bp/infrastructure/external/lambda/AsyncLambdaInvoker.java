package com.ms.bp.infrastructure.external.lambda;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.lambda.LambdaClient;
import software.amazon.awssdk.services.lambda.model.InvocationType;
import software.amazon.awssdk.services.lambda.model.InvokeRequest;
import software.amazon.awssdk.services.lambda.model.InvokeResponse;

/**
 * 非同期Lambda呼び出し用ユーティリティクラス
 * メインLambda関数からWorker Lambda関数への非同期呼び出しを管理
 */
public class AsyncLambdaInvoker {
    
    private static final Logger logger = LoggerFactory.getLogger(AsyncLambdaInvoker.class);
    
    /**
     * Worker Lambda関数名
     * 環境変数WORKER_FUNCTION_NAMEから取得
     */
    private final String workerFunctionName;
    
    /**
     * Lambda クライアント
     */
    private final LambdaClient lambdaClient;
    
    /**
     * JSON シリアライゼーション用ObjectMapper
     */
    private final ObjectMapper objectMapper;
    
    /**
     * コンストラクタ
     * 環境変数からWorker関数名を取得し、LambdaClientを初期化
     */
    public AsyncLambdaInvoker() {
        this.workerFunctionName = System.getenv("WORKER_FUNCTION_NAME");
        this.lambdaClient = LambdaClient.create();
        this.objectMapper = new ObjectMapper();
        
        if (this.workerFunctionName == null || this.workerFunctionName.isEmpty()) {
            logger.warn("WORKER_FUNCTION_NAME環境変数が設定されていません");
        }
        
        logger.info("AsyncLambdaInvokerが初期化されました: workerFunction={}", this.workerFunctionName);
    }
    
    /**
     * Worker Lambda関数を非同期で呼び出し
     * 
     * @param payload Worker Lambda関数に渡すペイロードデータ
     * @throws ServiceException 非同期呼び出しに失敗した場合
     */
    public void invokeAsync(WorkerPayload payload) throws JsonProcessingException {
        try {
            logger.info("Worker Lambda非同期呼び出し開始: jobId={}, operationType={}", 
                       payload.getJobId(), payload.getOperationType());
            
            // Worker関数名の検証
            if (workerFunctionName == null || workerFunctionName.isEmpty()) {
                throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                        "Worker Lambda関数名が設定されていません");
            }
            
            // ペイロードをJSON文字列に変換
            String payloadJson = objectMapper.writeValueAsString(payload);
            logger.debug("Worker Lambda呼び出しペイロード: {}", payloadJson);
            
            // 非同期呼び出しリクエストを構築
            InvokeRequest invokeRequest = InvokeRequest.builder()
                    .functionName(workerFunctionName)
                    .invocationType(InvocationType.EVENT)  // 非同期呼び出し
                    .payload(SdkBytes.fromUtf8String(payloadJson))
                    .build();
            
            // Worker Lambda関数を非同期で呼び出し
            InvokeResponse response = lambdaClient.invoke(invokeRequest);
            
            // 呼び出し結果をログ出力
            logger.info("Worker Lambda非同期呼び出し完了: jobId={}, statusCode={}", 
                       payload.getJobId(), response.statusCode());
            
            // ステータスコードが200番台でない場合はエラーとして扱う
            if (response.statusCode() < 200 || response.statusCode() >= 300) {
                throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                        "Worker Lambda呼び出しが失敗しました: statusCode=" + response.statusCode());
            }
            
        } catch (Exception e) {
            logger.error("Worker Lambda非同期呼び出しエラー: jobId={}, error={}", 
                        payload.getJobId(), e.getMessage(), e);
            
            if (e instanceof ServiceException) {
                throw e;
            }
            
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "Worker Lambda非同期呼び出し中にエラーが発生しました: " + e.getMessage());
        }
    }
    
    /**
     * リソースクリーンアップ
     * LambdaClientを閉じる
     */
    public void close() {
        if (lambdaClient != null) {
            lambdaClient.close();
            logger.info("AsyncLambdaInvokerのリソースをクリーンアップしました");
        }
    }
}
