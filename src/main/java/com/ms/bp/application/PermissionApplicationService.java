package com.ms.bp.application;

import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.repository.AreaCodeRepository;
import com.ms.bp.domain.permission.PermissionService;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponse;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 権限アプリケーションサービス
 * 権限関連のユースケースを調整し、ドメインサービスを組み合わせて処理を実行する
 * 権限は読み取り専用操作のため、読み取り専用実行を使用する
 */
public class PermissionApplicationService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionApplicationService.class);
    /** MS戦略・コーポOF判定コード */
    private static final String MS_STRATEGY_CORPO_HANT_CODE = "2";
    /**
     * ユーザーの全権限を取得（ページ初期化用）
     * @param userInfo 認証済みユーザー情報
     * @return ユーザー権限レスポンス
     */
    public UserPermissionsResponse getUserPermissions(UserInfo userInfo, String operationType) {
        logger.info("ユーザー権限取得開始: ユーザーID={}, 社員コード={}",
                   userInfo.getShainCode(), userInfo.getShainCode());

        // 読み取り専用でデータベースリソースを使用して権限を取得
        List<UserPermissionInfo> permissions = LambdaResourceManager.executeReadOnly(serviceFactory -> {
            PermissionService permissionService = serviceFactory.createPermissionService();
            List<UserPermissionInfo> result = permissionService.getUserPermissions(userInfo);

            logger.info("ユーザー権限取得完了: ユーザーID={}, 権限数={}",
                       userInfo.getShainCode(), result.size());

            return result;
        });

        if (BusinessConstants.OPERATION_UPLOAD_CODE.equals(operationType) || BusinessConstants.OPERATION_DOWNLOAD_CODE.equals(operationType)) {
            permissions = permissions.stream().filter(p-> operationType.equals(p.getOperationDivision())).toList();
        }

        // レスポンスDTOに変換（エリア情報も含む）
        return createUserPermissionsResponse(userInfo, permissions);
    }



    // ==================== DTO変換メソッド ====================

    /**
     * ユーザー権限レスポンスを作成
     * areaPatternが"1"の権限が存在する場合、エリア情報も取得して設定する
     * @param userInfo ユーザー情報
     * @param permissions 権限リスト
     * @return ユーザー権限レスポンス
     */
    private UserPermissionsResponse createUserPermissionsResponse(UserInfo userInfo, List<UserPermissionInfo> permissions) {
        UserPermissionsResponse response = new UserPermissionsResponse();
        response.setSystemOperationCompanyCode(userInfo.getSystemOperationCompanyCode());
        response.setPositionCode(userInfo.getPositionCode());
        response.setUnitCode(userInfo.getUnitCode());
        response.setAreaCode(userInfo.getAreaCode());
        response.setAreaName(userInfo.getAreaName());
        response.setGroupCode(userInfo.getGroupCode());
        response.setShainCode(userInfo.getShainCode());

        // 権限情報を変換
        List<UserPermissionsResponse.PermissionInfo> permissionInfos = permissions.stream()
            .map(this::convertToPermissionInfo)
            .collect(Collectors.toList());
        response.setPermissions(permissionInfos);

        // 読み取り専用でデータベースリソースを使用してMS戦略・コーポOF判定コードに対応する種類コードリストを取得
        List<String> ruleType = LambdaResourceManager.executeReadOnly(serviceFactory -> {
            PermissionRepository  permissionRepository = serviceFactory.getPermissionRepository();
            return permissionRepository.findRuleTypeCodesByMsStrategyCorp(MS_STRATEGY_CORPO_HANT_CODE, userInfo.getSystemOperationCompanyCode());
        });
        //ユーザがMS_戦略・コーポOFに含まれるユニットに所属している以外
        boolean isRuleType = !ruleType.isEmpty() && !ruleType.contains(userInfo.getUnitCode());

        //役職区分判定要否 0:否 1:要
        response.setPositionSpecialCheck(needsSpecialCheck(permissions, isRuleType));
        // areaPatternが"1"の権限が存在する場合、エリア情報を取得して設定
        boolean hasAreaSpecificPermission = permissions.stream()
            .anyMatch(permission -> BusinessConstants.AREA_PATTERN_AREA_SPECIFIC.equals(permission.getAreaPattern()));

        if (hasAreaSpecificPermission) {
            logger.debug("エリア固有権限が検出されました。エリア情報を取得します: ユーザー={}", userInfo.getShainCode());
            List<AreaInfo> areaInfos = LambdaResourceManager.executeReadOnly(serviceFactory -> {
                PermissionRepository permissionRepository = serviceFactory.getPermissionRepository();
                return permissionRepository.findAreaInfosByAreaTantoshaPermission(userInfo.getSystemOperationCompanyCode());
            });
            response.setAreaInfos(areaInfos);
        } else {
            response.setAreaInfos(List.of(AreaInfo.builder()
                    .areaCode(userInfo.getAreaCode()).areaName(userInfo.getAreaName()).build()));
        }

        return response;
    }

    /**
     * UserPermissionInfoをPermissionInfoに変換
     * @param userPermission ユーザー権限情報
     * @return 権限情報DTO
     */
    private UserPermissionsResponse.PermissionInfo convertToPermissionInfo(UserPermissionInfo userPermission) {
        UserPermissionsResponse.PermissionInfo permissionInfo = new UserPermissionsResponse.PermissionInfo();
        permissionInfo.setPermissionCode(userPermission.getPermissionCode());
        permissionInfo.setFileTypeCode(userPermission.getFileTypeCode());
        permissionInfo.setOperationDivision(userPermission.getOperationDivision());
        permissionInfo.setAreaPattern(userPermission.getAreaPattern());
        permissionInfo.setHantCode(userPermission.getHantCode());
        return permissionInfo;
    }

    /**
     * 指定されたルールに基づき、特別なチェック（役職区分判定要否）が必要かどうかを判定します。
     *
     * @param permissions ユーザーの権限リスト
     * @return "1" (要), "0" (否)
     */
    public static String needsSpecialCheck(List<UserPermissionInfo> permissions, boolean isRuleType) {
        // 条件：ファイルタイプが "002"/"003" かつ エリア権限（3桁目 'A'） かつ ユーザがMS_戦略・コーポOFに含まれるユニットに所属している以外場合
        return permissions.stream()
                .anyMatch(p -> {
                    var code = p.getPermissionCode();
                    return (BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE.equals(p.getFileTypeCode()) || BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE.equals(p.getFileTypeCode())) &&
                            code != null && code.length() > 2 && code.charAt(2) == 'A' && isRuleType;
                }) ? "1" : "0";
    }

}
