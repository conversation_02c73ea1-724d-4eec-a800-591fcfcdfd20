package com.ms.bp.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * エクスポートジョブ結果データ
 * updateJobメソッドのパラメータ簡素化のためのDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExportJobResult {

    // ファイル情報
    private String s3Key;
    private String fileName;
    private Long fileSize;

    /**
     * 完了時の結果データを作成
     */
    public static ExportJobResult completed(String s3Key, String fileName, Long fileSize) {
        return ExportJobResult.builder()
                .s3Key(s3Key)
                .fileName(fileName)
                .fileSize(fileSize)
                .build();
    }
}
