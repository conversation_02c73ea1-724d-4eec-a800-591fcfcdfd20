package com.ms.bp.application;

import com.ms.bp.application.dto.UserBusinessInfo;
import com.ms.bp.application.factory.DomainServiceFactory;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.domain.master.model.UserBasicInfo;
import com.ms.bp.domain.master.repository.UserMasterRepository;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.domain.user.UserBusinessDomainService;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

/**
 * ユーザー業務情報アプリケーションサービス
 * ログインユーザーの業務関連情報取得に関するビジネスロジックを提供
 * 社員マスタ、ユニットマスタ、グループマスタを連携してユーザーの所属情報を取得
 */
public class UserInfoApplicationService {
    private static final Logger logger = LoggerFactory.getLogger(UserInfoApplicationService.class);

    /**
     * ユーザーの業務情報を取得
     * 社員マスタ、ユニットマスタ、グループマスタを連携して一括取得し、
     * エリアコードに基づいて所属区分を判定する
     *
     * @param userInfo 認証済みユーザー情報
     * @return ユーザー業務情報DTO
     * @throws ServiceException 業務エラー（データが見つからない場合など）
     */
    public UserBusinessInfo getUserBusinessInfo(UserInfo userInfo) {
        logger.info("ユーザー業務情報取得開始: 社員コード={}, 企業コード={}",
                   userInfo.getShainCode(), userInfo.getSystemOperationCompanyCode());

        // 読み取り専用でデータベースリソースを使用してユーザー業務情報を取得
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            try {
                // 社員マスタ、ユニットマスタ、グループマスタを連携して一括取得
                UserBasicInfo userBasicInfo = getBasicInfo(serviceFactory, userInfo);

                // ドメインサービスを使用して所属区分を判定
                UserBusinessDomainService userBusinessDomainService = serviceFactory.getUserBusinessDomainService();
                String affiliationDivision = userBusinessDomainService.determineAffiliationDivision(userBasicInfo.getAreaCode());

                // DTOを構築して返却
                UserBusinessInfo result = UserBusinessInfo.builder()
                        .unitCode(userBasicInfo.getUnitCode())
                        .positionCode(userBasicInfo.getPositionCode())
                        .affiliationDivision(affiliationDivision)
                        .areaCode(userBasicInfo.getAreaCode())
                        .areaName(userBasicInfo.getAreaName())
                        .shainCode(userInfo.getShainCode())
                        .systemOperationCompanyCode(userInfo.getSystemOperationCompanyCode())
                        .groupCode(userBasicInfo.getGroupCode())
                        .build();

                logger.info("ユーザー業務情報取得完了: 社員コード={}, ユニットコード={}, 所属区分={}, エリアコード={}",
                           userInfo.getShainCode(), result.getUnitCode(),
                           result.getAffiliationDivision(), result.getAreaCode());

                return result;

            } catch (Exception e) {
                logger.error("ユーザー業務情報取得中にエラーが発生しました: 社員コード={}",
                           userInfo.getShainCode(), e);
                throw new ServiceException(GlobalMessageConstants.BUSINESS_ERROR,
                                         "ユーザー業務情報の取得に失敗しました");
            }
        });
    }

    /**
     * ユーザーマスタからユーザー基本情報を取得
     * 社員マスタ、ユニットマスタ、グループマスタ、組織エリアマスタを連携して一括取得
     * システム運用企業コードと社員コードを使用して業務に必要な全情報を取得
     *
     * @param serviceFactory ドメインサービスファクトリ
     * @param userInfo ユーザー情報
     * @return ユーザー基本情報（ユニットコード、役職区分コード、エリアコード、エリア名称を含む）
     * @throws ServiceException ユーザー情報が見つからない場合
     */
    private UserBasicInfo getBasicInfo(DomainServiceFactory serviceFactory, UserInfo userInfo) {
             UserMasterRepository repository = serviceFactory.getUserMasterRepository();
        Optional<UserBasicInfo> basicInfo = repository.findUserBasicInfo(
            userInfo.getSystemOperationCompanyCode(),
            userInfo.getShainCode()
        );

        if (basicInfo.isEmpty()) {
            logger.warn("ユーザー基本情報が見つかりませんでした: 企業コード={}, 社員コード={}",
                       userInfo.getSystemOperationCompanyCode(), userInfo.getShainCode());
            throw new ServiceException(GlobalMessageConstants.NOT_FOUND,
                                     "ユーザーマスタから情報を取得できませんでした");
        }

        return basicInfo.get();
    }

}
