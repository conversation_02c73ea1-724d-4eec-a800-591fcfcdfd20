package com.ms.bp.application.data.strategy;

import com.ms.bp.application.data.FileExportOrchestrator.ExportTask;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;

import java.util.List;

/**
 * ファイル分割戦略インターフェース
 * エクスポート時のファイル分割ロジックを定義
 */
public interface FileSplitStrategy {

    /**
     * エクスポートタスクを作成
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return エクスポートタスクリスト
     */
    List<ExportTask> createExportTasks(ExportRequest exportRequest, UserInfo userInfo);
}
