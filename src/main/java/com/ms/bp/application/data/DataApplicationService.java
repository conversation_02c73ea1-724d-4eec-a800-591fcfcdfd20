package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.PermissionApplicationService;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.application.ExportJobStatusService;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.domain.file.model.ExportJobStatus;
import com.ms.bp.domain.file.model.ImportJobStatus;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.interfaces.dto.response.*;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.shared.util.RequestContext;
import com.ms.bp.shared.util.RequestConversionUtil;
import com.ms.bp.interfaces.dto.request.DownloadUrlRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.util.FunctionUtil;
import com.ms.bp.infrastructure.external.s3.S3Service;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * データ管理アプリケーションサービス
 * データのインポート・エクスポート処理のビジネスロジックを協調する
 */
public class DataApplicationService {
    private static final Logger logger = LoggerFactory.getLogger(DataApplicationService.class);

    // 依存サービス
    private final PermissionApplicationService permissionService;
    private final ImportJobStatusService importJobStatusService;
    private final ExportJobStatusService exportJobStatusService;
    private final TaskOrchestrationService taskOrchestrationService;
    private final S3Service s3Service;
    private final AsyncLambdaInvoker asyncLambdaInvoker;

    public DataApplicationService() {
        this.permissionService = new PermissionApplicationService();
        this.importJobStatusService = new ImportJobStatusService();
        this.exportJobStatusService = new ExportJobStatusService();
        this.taskOrchestrationService = new TaskOrchestrationService();
        this.s3Service = new S3Service();
        this.asyncLambdaInvoker = new AsyncLambdaInvoker();
    }

    /**
     * エクスポート処理を開始
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @param context Lambda実行コンテキスト
     * @return エクスポートジョブレスポンス
     */
    public ExportJobResponse startExport(ExportRequest exportRequest, UserInfo userInfo, Context context) {
        try {
            logger.debug("エクスポート処理開始: ユーザー={}, データタイプ={}",
                       userInfo.getShainCode(), exportRequest.getDataType());
            String functionId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_DOWNLOAD_CODE, exportRequest.getDataType()).getFunctionId();

            // ジョブステータスを初期化（データベースに保存）
            ExportJobStatus jobStatus = ExportJobStatus.builder()
                    .systmUnyoKigyoCode(userInfo.getSystemOperationCompanyCode())
                    .shainCode(userInfo.getShainCode())
                    .fileShbts(exportRequest.getDataType())
                    .area(exportRequest.getAreaString())
                    .hnshBashoKubun(exportRequest.getHnshBashoKubun())
                    .dataKubun(exportRequest.getDataKubunString())
                    .ctgryKubun(exportRequest.getCtgryKubun())
                    .build().init(functionId);

            // データベースに保存し、自動生成された履歴番号を取得
            String rrkBango = exportJobStatusService.createJob(jobStatus).toString();

            // Worker Lambda用ペイロードを構築
            WorkerPayload payload = WorkerPayload.builder()
                    .jobId(rrkBango)
                    .operationType(BusinessConstants.OPERATION_DOWNLOAD_CODE)
                    .request(exportRequest)
                    .userInfo(userInfo)
                    .build();

            // Worker Lambda関数を非同期で呼び出し
            asyncLambdaInvoker.invokeAsync(payload);

            logger.info("エクスポートジョブを受け付けました（非同期処理開始）: jobId={}", rrkBango);

            return ExportJobResponse.accepted(rrkBango);

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("エクスポート処理開始エラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "エクスポート処理の開始中にエラーが発生しました");
        }
    }

    /**
     * インポート処理を開始
     * @param importRequest インポートリクエスト
     * @param userInfo ユーザー情報
     * @param context Lambda実行コンテキスト
     * @return インポートジョブレスポンス
     */
    public ImportJobResponse startImport(ImportRequest importRequest, UserInfo userInfo, Context context) {
        try {
            logger.info("インポート処理開始: ユーザー={}, データタイプ={}",
                       userInfo.getShainCode(), importRequest.getDataType());
            String functionId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE, importRequest.getDataType()).getFunctionId();

            // ジョブステータスを初期化（データベースに保存）
            ImportJobStatus jobStatus = ImportJobStatus.builder()
                    .systmUnyoKigyoCode(userInfo.getSystemOperationCompanyCode())
                    .shainCode(userInfo.getShainCode())
                    .fileShbts(importRequest.getDataType())
                    .area(importRequest.getAreaString())
                    .build().init(functionId);

            // データベースに保存し、自動生成された履歴番号を取得
            String rrkBango = importJobStatusService.createJob(jobStatus).toString();

            // Worker Lambda用ペイロードを構築
            WorkerPayload payload = WorkerPayload.builder()
                    .jobId(rrkBango)
                    .operationType(BusinessConstants.OPERATION_UPLOAD_CODE)
                    .request(importRequest)
                    .userInfo(userInfo)
                    .build();

            // Worker Lambda関数を非同期で呼び出し
            asyncLambdaInvoker.invokeAsync(payload);

            logger.info("インポートジョブを受け付けました（非同期処理開始）: jobId={}", rrkBango);

            return ImportJobResponse.accepted(rrkBango);

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("インポート処理開始エラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "インポート処理の開始中にエラーが発生しました");
        }
    }

    /**
     * エクスポート履歴を取得
     * 分页処理は不要、デフォルトで最新20件のデータを取得する
     * ファイル種別やエリア名を日本語表示に変換して返却する
     *
     * @param userInfo ユーザー情報
     * @return エクスポート履歴リスト（最新20件）
     */
    public List<ExportHistoryResponse> getExportHistory(UserInfo userInfo) {
        try {
            logger.debug("エクスポート履歴取得開始: ユーザー={}", userInfo.getShainCode());

            // ExportJobStatusServiceから最新20件のエクスポート履歴を取得
            List<ExportHistoryResponse> historyList = exportJobStatusService.getExportHistoryList(
                userInfo.getShainCode(), userInfo.getSystemOperationCompanyCode());

            logger.debug("エクスポート履歴取得完了: ユーザー={}, 取得件数={}",
                       userInfo.getShainCode(), historyList.size());

            return historyList;

        } catch (Exception e) {
            logger.error("エクスポート履歴取得エラー: ユーザー={}, エラー内容={}",
                        userInfo.getShainCode(), e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "エクスポート履歴取得中にエラーが発生しました");
        }
    }

    /**
     * インポート履歴を取得
     * 分页処理は不要、デフォルトで最新20件のデータを取得する
     * ファイル種別やエリア名を日本語表示に変換して返却する
     *
     * @param userInfo ユーザー情報
     * @return インポート履歴リスト（最新20件）
     */
    public List<ImportHistoryResponse> getImportHistory(UserInfo userInfo) {
        try {
            logger.debug("インポート履歴取得開始: ユーザー={}", userInfo.getShainCode());

            // ImportJobStatusServiceから最新20件のインポート履歴を取得
            List<ImportHistoryResponse> historyList = importJobStatusService.getImportHistoryList(
                userInfo.getShainCode(),userInfo.getSystemOperationCompanyCode());

            logger.debug("インポート履歴取得完了: ユーザー={}, 取得件数={}",
                       userInfo.getShainCode(), historyList.size());

            return historyList;

        } catch (Exception e) {
            logger.error("インポート履歴取得エラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "インポート履歴取得中にエラーが発生しました");
        }
    }

    /**
     * 履歴番号に基づいてダウンロードURLを生成（リクエストオブジェクト版）
     * 操作タイプに応じて異なる履歴テーブルから情報を取得し、
     * ファイル情報に基づいてS3ダウンロードURLを生成する
     *
     * @param downloadRequest ダウンロードURLリクエスト
     * @param userInfo ユーザー情報
     * @return ダウンロードURL情報
     */
    public DownloadUrlResponse generateDownloadUrlByHistory(DownloadUrlRequest downloadRequest, UserInfo userInfo) {
        try {

            String operationType = downloadRequest.getOperationType();
            Long rrkBango = downloadRequest.getRrkBango();
            Integer expiresInSeconds = downloadRequest.getExpiresInSeconds();
            logger.info("履歴ベースダウンロードURL生成開始: 履歴番号={}, 操作タイプ={}, ユーザー={}",
                    rrkBango, operationType, userInfo.getShainCode());

            String s3Key;
            if (BusinessConstants.OPERATION_DOWNLOAD_CODE.equals(operationType)) {
                // エクスポート履歴からファイル情報を取得
                ExportJobStatus exportJob = exportJobStatusService.getJobStatus(rrkBango);
                if (exportJob == null) {
                    throw new ServiceException(GlobalMessageConstants.NOT_FOUND.getCode(),
                            "指定された履歴番号のエクスポート履歴が見つかりません: " + rrkBango);
                }

                // ダウンロード可能状態の確認
                if (!exportJob.isDownloadable()) {
                    throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                            "ファイルがダウンロード可能な状態ではありません。処理が完了していないか、ファイルが存在しません。");
                }

                // エクスポートファイルのS3パスを生成
                s3Key = exportJob.getZipFileMei();

            } else {
                // インポート履歴からファイル情報を取得
                ImportJobStatus importJob = importJobStatusService.getJobStatus(rrkBango);
                if (importJob == null) {
                    throw new ServiceException(GlobalMessageConstants.NOT_FOUND.getCode(),
                            "指定された履歴番号のインポート履歴が見つかりません: " + rrkBango);
                }

                // エラーファイル
                s3Key = importJob.getErrorFileMei();
                if (s3Key == null || s3Key.trim().isEmpty()) {
                    throw new ServiceException(GlobalMessageConstants.NOT_FOUND.getCode(),
                            "エラーファイルが存在しません");
                }
            }

            // S3署名付きダウンロードURLを生成
            String downloadUrl = s3Service.getSignedDownloadUrl(s3Key, expiresInSeconds);

            // レスポンスを作成
            DownloadUrlResponse response = new DownloadUrlResponse();
            response.setDownloadUrl(downloadUrl);

            // ファイルサイズを取得（オプション）
            try {
                var metadata = s3Service.getObjectMetadata(s3Key);
                if (metadata.containsKey("fileSize")) {
                    response.setFileSize((Long) metadata.get("fileSize"));
                }
            } catch (Exception e) {
                logger.warn("ファイルサイズの取得に失敗しました: {}", e.getMessage());
                // ファイルサイズの取得失敗は致命的ではないため、処理を続行
            }

            logger.info("履歴ベースダウンロードURL生成成功: 履歴番号={}, S3キー={}, URL={}",
                    rrkBango, s3Key, downloadUrl);
            return response;

        } catch (ServiceException e) {
            // ServiceExceptionはそのまま再スロー
            throw e;
        } catch (Exception e) {
            logger.error("履歴ベースダウンロードURL生成エラー:  エラー={}",  e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "ダウンロードURL生成中にエラーが発生しました: " + e.getMessage());
        }
    }

    // ==================== プライベートメソッド ====================

    /**
     * エクスポート権限を検証
     * @param userInfo ユーザー情報
     * @param dataType データタイプ
     * @throws ServiceException 権限がない場合
     */
    private void validatePermission(UserInfo userInfo, String dataType, String operationType) {
        try {
            logger.info("エクスポート権限チェック開始: ユーザー={}, データタイプ={}", userInfo.getShainCode(), dataType);

            if (BusinessConstants.OPERATION_UPLOAD_CODE.equals(operationType)) {
                UserPermissionsResponse result = permissionService.getUserPermissions(userInfo, operationType);
                userInfo.setAreaInfos(result.getAreaInfos());
            } else if (BusinessConstants.OPERATION_DOWNLOAD_CODE.equals(operationType) && BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE.equals(dataType)) {
                // ダウンロード権限をチェック（エクスポート = ダウンロード）
                UserPermissionsResponse result = permissionService.getUserPermissions(userInfo, operationType);
                userInfo.setPositionSpecialCheck(result.getPositionSpecialCheck());
            }

            logger.info("エクスポート権限チェック成功: ユーザー={}, データタイプ={}", userInfo.getShainCode(), dataType);

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("エクスポート権限チェック中にエラーが発生しました", e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "権限チェック処理中にエラーが発生しました");
        }
    }

   

    /**
     * Worker Lambda用インポート処理実行
     * WorkerHandlerから呼び出され、実際のインポート処理を実行
     *
     * @param payload WorkerPayloadオブジェクト
     * @param context Lambda実行コンテキスト
     */
    public void executeImportTask(WorkerPayload payload, Context context) {
        try {
            logger.info("Worker Lambda用インポート処理開始: jobId={}", payload.getJobId());

            // リクエストオブジェクトをImportRequestに変換
            ImportRequest importRequest = RequestConversionUtil.convertRequest(payload.getRequest(), ImportRequest.class);

            // RequestContextにImportRequestを設定（非同期処理での統一管理）
            RequestContext.setImportRequest(importRequest);
            logger.debug("ImportRequestをRequestContextに設定: dataType={}, s3Key={}",
                        importRequest.getDataType(), importRequest.getS3Key());

            // 権限チェック
            validatePermission(payload.getUserInfo(), importRequest.getDataType(),BusinessConstants.OPERATION_UPLOAD_CODE);

            // TaskOrchestrationServiceを使用してインポート処理を実行
            taskOrchestrationService.orchestrateImportTask(
                    Long.parseLong(payload.getJobId()),
                    importRequest,
                    payload.getUserInfo(),
                    context
            );

            logger.info("Worker Lambda用インポート処理正常完了: jobId={}", payload.getJobId());

        } catch (Exception e) {
            logger.error("Worker Lambda用インポート処理エラー: jobId={}, error={}",
                        payload.getJobId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Worker Lambda用エクスポート処理実行
     * WorkerHandlerから呼び出され、実際のエクスポート処理を実行
     *
     * @param payload WorkerPayloadオブジェクト
     * @param context Lambda実行コンテキスト
     */
    public void executeExportTask(WorkerPayload payload, Context context) {
        try {
            logger.info("Worker Lambda用エクスポート処理開始: jobId={}", payload.getJobId());

            // リクエストオブジェクトをExportRequestに変換
            ExportRequest exportRequest = RequestConversionUtil.convertRequest(payload.getRequest(), ExportRequest.class);

            // 権限チェック
            validatePermission(payload.getUserInfo(), exportRequest.getDataType(),BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // TaskOrchestrationServiceを使用してエクスポート処理を実行
            taskOrchestrationService.orchestrateExportTask(
                    Long.parseLong(payload.getJobId()),
                    exportRequest,
                    payload.getUserInfo(),
                    context
            );

            logger.info("Worker Lambda用エクスポート処理正常完了: jobId={}", payload.getJobId());

        } catch (Exception e) {
            logger.error("Worker Lambda用エクスポート処理エラー: jobId={}, error={}",
                        payload.getJobId(), e.getMessage(), e);
            throw e;
        }
    }
}
