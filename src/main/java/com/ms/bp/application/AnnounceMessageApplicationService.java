package com.ms.bp.application;

import com.ms.bp.domain.file.model.AnnounceMessage;
import com.ms.bp.domain.master.repository.AnnounceMessageRepository;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.AnnounceMessageResponse;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.util.LambdaResourceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;

/**
 * アナウンスメッセージサービス
 * アナウンスメッセージに関するビジネスロジックを提供
 */
public class AnnounceMessageApplicationService {
    private static final Logger logger = LoggerFactory.getLogger(AnnounceMessageApplicationService.class);

    /**
     * アナウンスメッセージを取得（ページ初期化用）
     * @param userInfo 認証済みユーザー情報
     * @param msgKbn メッセージ区分
     * @return アナウンスメッセージレスポンス
     */
    public AnnounceMessageResponse getAnnounceMessage(UserInfo userInfo, String msgKbn) {
        try {
            logger.info("アナウンスメッセージ取得開始: ユーザーID={}", userInfo.getShainCode());

            // 読み取り専用でデータベースリソースを使用して権限を取得
            List<AnnounceMessage> announceMessages = LambdaResourceManager.executeReadOnly(serviceFactory -> {
                AnnounceMessageRepository repository = serviceFactory.getAnnounceMessageRepository();
                return repository.findByMsgKubun(msgKbn);
            });

            logger.info("アナウンスメッセージ取得完了: ユーザーID={}, アナウンスメッセージ={}",
                    userInfo.getShainCode(), announceMessages);
            return AnnounceMessageResponse.builder().msgInfos(announceMessages).build();

        } catch (Exception e) {
            logger.error("アナウンスメッセージ取得中にエラーが発生しました: ユーザー={}", userInfo.getShainCode(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "アナウンスメッセージの取得に失敗しました");
        }
    }
}