package com.ms.bp.application;

import com.ms.bp.application.dto.ExportJobResult;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.repository.AreaCodeRepository;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.domain.file.repository.ExportJobStatusRepository;
import com.ms.bp.domain.file.model.ExportJobStatus;
import com.ms.bp.interfaces.dto.response.ExportHistoryResponse;

import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * エクスポートジョブステータスサービス
 * エクスポートジョブの状態管理に関するビジネスロジックを提供
 */
public class ExportJobStatusService {
    private static final Logger logger = LoggerFactory.getLogger(ExportJobStatusService.class);


    /**
     * 新しいエクスポートジョブを作成し、自動生成された履歴番号を返す
     * @param jobStatus エクスポートジョブステータス
     * @return 自動生成された履歴番号
     */
    public Long createJob(ExportJobStatus jobStatus) {
        return LambdaResourceManager.executeWithTransaction(serviceFactory -> {
            ExportJobStatusRepository repository = serviceFactory.getExportJobStatusRepository();

            // データベースに挿入（自動生成された履歴番号がjobStatusに設定される）
            repository.save(jobStatus);

            // 自動生成された履歴番号を返す
            return jobStatus.getRrkBango();
        });
    }



    /**
     * ジョブステータスを更新（統合メソッド）
     * @param rrkBango 履歴番号
     * @param status ステータス
     * @param result 処理結果データ（完了時、一部失敗時、失敗時に必要）
     */
    public void updateJob(Long rrkBango, String status, ExportJobResult result) {
        LambdaResourceManager.executeWithTransaction(serviceFactory -> {
            ExportJobStatusRepository repository = serviceFactory.getExportJobStatusRepository();

            // 既存のジョブステータスを取得
            ExportJobStatus jobStatus = repository.findByRrkBango(rrkBango);
            if (jobStatus == null) {
                logger.warn("ジョブステータスが見つかりません: rrkBango={}", rrkBango);
                return null;
            }
            // 共通変数の準備
            String s3Key = Optional.ofNullable(result)
                    .map(ExportJobResult::getS3Key)
                    .orElse(null);
            String fileSize = Optional.ofNullable(result)
                    .map(ExportJobResult::getFileSize)
                    .map(size -> (size == 0 ? 0 : Math.ceilDiv(size, 1024)) + " KB")
                    .orElse(null);

            switch (status) {
                case BusinessConstants.BATCH_STATUS_COMPLETED_CODE:  // 完了
                    jobStatus.updateCompletionStats(s3Key, fileSize);
                    logger.info("エクスポートジョブを完了状態に更新しました: rrkBango={}, s3Key={}, fileSize={}",
                            rrkBango, s3Key, fileSize);
                    break;
                case BusinessConstants.BATCH_STATUS_PARTIALLY_FAILED_CODE:  // 一部失敗
                    jobStatus.updatePartialFailureStatus(s3Key, fileSize);
                    logger.warn("エクスポートジョブを一部失敗状態に更新しました: rrkBango={}, s3Key={}, fileSize={}",
                            rrkBango, s3Key, fileSize);
                    break;
                case BusinessConstants.BATCH_STATUS_FAILED_CODE:  // 失敗
                    jobStatus.updateFailureStatus(s3Key, fileSize, false);
                    logger.warn("エクスポートジョブを失敗状態に更新しました: rrkBango={}, s3Key={}, fileSize={}",
                            rrkBango, s3Key, fileSize);
                    break;

                case BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE:  // システムエラー
                    jobStatus.updateFailureStatus(null, null, true);
                    logger.error("エクスポートジョブをシステムエラー状態に更新しました: rrkBango={}", rrkBango);
                    break;
            }

            // データベースを更新
            repository.update(jobStatus);
            logger.debug("エクスポートジョブステータスを更新しました: rrkBango={}, status={}", rrkBango, jobStatus.getStts());

            return null;
        });
    }

    /**
     * 履歴番号でエクスポートジョブステータスを取得
     * @param rrkBango 履歴番号
     * @return エクスポートジョブステータス
     */
    public ExportJobStatus getJobStatus(Long rrkBango) {
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            ExportJobStatusRepository repository = serviceFactory.getExportJobStatusRepository();
            return repository.findByRrkBango(rrkBango);
        });
    }

    /**
     * エクスポート履歴リストを取得（最新20件）
     * M_SOSHIKIAREAMSTテーブルと連表查询してエリア名も一緒に取得し、
     * SQL内でCASE WHEN文によりエリア名変換を実行して返却する
     *
     * @param shainCode 社員コード
     * @return エクスポート履歴リスト（最新20件）
     */
    public List<ExportHistoryResponse> getExportHistoryList(String shainCode,String systemOperationCompanyCode) {
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            ExportJobStatusRepository repository = serviceFactory.getExportJobStatusRepository();

            // 最新20件のエクスポートジョブステータスを取得（SQL内でエリア名変換済み）
            List<ExportJobStatus> jobStatusList = repository.findByShainCode(
                shainCode, systemOperationCompanyCode, BusinessConstants.DEFAULT_HISTORY_LIMIT, 0);

            if (CollectionUtils.isNotEmpty(jobStatusList)) {
                String nendo = DateUtil.getNextFiscalYear();

                // エリアコードマッピングを取得
                Map<String, String> areaMap = serviceFactory.getAreaCodeRepository()
                        .findAreaInfosByNendo(nendo, systemOperationCompanyCode)
                        .stream()
                        .collect(Collectors.toMap(
                                AreaInfo::getAreaCode,
                                AreaInfo::getAreaName,
                                (existing, replacement) -> existing
                        ));

                // 各ジョブステータスのエリア名を更新
                jobStatusList.stream()
                        .filter(jobStatus -> StringUtils.isNotEmpty(jobStatus.getArea()))
                        .forEach(jobStatus -> {
                            String areaNames = Arrays.stream(jobStatus.getArea().split(","))
                                    .map(code -> {
                                        if ("SKSA".equals(code)) {
                                            return "採算管理単位計画策定エリア";
                                        } else {
                                            return areaMap.getOrDefault(code.trim(), "");
                                        }
                                    })
                                    .collect(Collectors.joining(","));
                            jobStatus.setAreaName(areaNames);
                        });
            }

            // ExportHistoryResponseに変換
            return jobStatusList.stream()
                .map(this::convertToExportHistoryResponse)
                .collect(Collectors.toList());
        });
    }

    /**
     * ExportJobStatusをExportHistoryResponseに変換（SQL内で全変換済み）
     * SQL内でCASE WHEN文により変換されたファイル種別、エリア名、本社場所区分、データ区分、ステータス、カテゴリー区分を直接使用
     *
     * @param jobStatus エクスポートジョブステータス（SQL内で全変換済み）
     * @return エクスポート履歴レスポンス
     */
    private ExportHistoryResponse convertToExportHistoryResponse(ExportJobStatus jobStatus) {
        return ExportHistoryResponse.builder()
            .historyNumber(jobStatus.getRrkBango())
            .fileType(jobStatus.getFileShbtsName()) // SQL内で変換済み
            .area(jobStatus.getAreaName()) // SQL内で変換済み
            .headquartersLocationDivision(jobStatus.getHnshBashoKubunName()) // SQL内で変換済み
            .dataDivision(jobStatus.getDataKubunName()) // SQL内で変換済み
            .fileCreationStartDateTime(DateUtil.parseDateTime(jobStatus.getFileSksKshNchj()))
            .fileCreationCompletionDateTime(DateUtil.parseDateTime(jobStatus.getFileSksKnrNchj()))
            .status(jobStatus.getSttsName()) // SQL内で変換済み
            .zipSize(jobStatus.getZipFileSize())
            .zipFileName(jobStatus.getZipFileMei())
            .ctgryKubun(jobStatus.getCtgryKubunName()) // SQL内で変換済み
            .build();
    }

}
