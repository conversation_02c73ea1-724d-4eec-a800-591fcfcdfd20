package com.ms.bp.interfaces.dto.request;

import com.ms.bp.domain.user.model.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Worker Lambda関数への非同期呼び出し用データ転送オブジェクト
 * メインLambda関数からWorker Lambda関数へのデータ受け渡しに使用
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkerPayload {
    
    /**
     * ジョブID（履歴番号）
     * データベースの履歴テーブルの主キーとして使用
     */
    private String jobId;
    
    /**
     * 操作タイプ
     * "1": インポート処理
     * "2": エクスポート処理
     */
    private String operationType;
    
    /**
     * リクエストオブジェクト
     * ImportRequestまたはExportRequestオブジェクトをJSON形式で格納
     */
    private Object request;
    
    /**
     * ユーザー情報
     * 権限チェックや処理実行に必要なユーザー情報
     */
    private UserInfo userInfo;
    
    /**
     * 追加コンテキスト情報
     * 必要に応じて追加の処理パラメータを格納
     */
    private Map<String, Object> context;
}
