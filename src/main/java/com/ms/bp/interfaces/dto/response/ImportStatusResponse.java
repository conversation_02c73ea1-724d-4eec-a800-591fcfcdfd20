package com.ms.bp.interfaces.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * インポートステータス確認用レスポンスDTO
 * インターフェース層のデータ転送オブジェクト
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportStatusResponse {
    /**
     * ジョブID
     */
    private String jobId;

    /**
     * ステータス
     * PENDING: 処理待ち
     * PROCESSING: 処理中
     * COMPLETED: 完了
     * FAILED: 失敗
     */
    private String status;

    /**
     * 進捗率（0-100）
     * -1の場合はエラー
     */
    private Integer progress;

    /**
     * メッセージ
     */
    private String message;

    /**
     * 処理済み件数
     */
    private Integer processedCount;

    /**
     * 挿入件数
     */
    private Integer insertedCount;

    /**
     * 更新件数
     */
    private Integer updatedCount;

    /**
     * 失敗件数
     */
    private Integer failedCount;

    /**
     * 総件数
     */
    private Integer totalCount;

    /**
     * データタイプ
     */
    private String dataType;

    /**
     * ソースファイル名
     */
    private String sourceFile;

    /**
     * エラーファイルのダウンロードURL（エラーがある場合のみ）
     */
    private String errorFileUrl;

    /**
     * エラーファイル名（エラーがある場合のみ）
     */
    private String errorFileName;

    /**
     * 作成日時
     */
    private Date createdAt;

    /**
     * 更新日時
     */
    private Date updatedAt;

    /**
     * 完了日時（完了時のみ）
     */
    private Date completedAt;

    /**
     * 実行時間（ミリ秒）
     */
    private Long executionTimeMs;
}
