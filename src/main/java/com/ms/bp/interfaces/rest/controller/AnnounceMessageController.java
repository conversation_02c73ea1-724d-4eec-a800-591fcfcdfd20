package com.ms.bp.interfaces.rest.controller;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.application.AnnounceMessageApplicationService;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.AnnounceMessageResponse;
import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.exception.ValidationException;
import com.ms.bp.shared.util.MessageCodeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.regex.Pattern;
import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

/**
 * アナウンスメッセージコントローラー
 * HTTPリクエスト/レスポンス処理のみを担当
 * ビジネスロジックはAnnounceMessageApplicationServiceに委譲
 */
public class AnnounceMessageController {
    private static final Logger logger = LoggerFactory.getLogger(AnnounceMessageController.class);
    /** 半角数字検証用の事前コンパイル済みパターン */
    private static final Pattern NUMERIC_HALF_WIDTH_PATTERN = Pattern.compile("^[0-9]+$");
    private final AnnounceMessageApplicationService messageApplicationService;
    private final ObjectMapper objectMapper;

    /**
     * コンストラクタ構築
     */
    public AnnounceMessageController() {
        this.objectMapper = new ObjectMapper();
        this.messageApplicationService=new AnnounceMessageApplicationService();
    }

    /**
     * アナウンスメッセージ取得API（ページ初期化用）
     * GET /api/message/announce
     */
    public CommonResult<?> getAnnounceMessage(APIGatewayProxyRequestEvent request, UserInfo userInfo) {
        try {
            logger.info(formatMessage(GlobalMessageConstants.INF_001, "共通処理.メッセージ取得"));

            String requestBody = request.getBody();
            if (requestBody == null || requestBody.trim().isEmpty()) {
                throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                        "リクエストボディが必要です");
            }

            // JSONからパラメータを解析
            var requestData = objectMapper.readValue(requestBody, java.util.Map.class);
            String msgKbn = (String) requestData.get("msgKbn");

            if (msgKbn == null || msgKbn.trim().isEmpty()) {
                throw new ValidationException(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "メッセージ区分"));
            }

            if(msgKbn.length() !=1 ){
                 throw new ValidationException(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_020, "メッセージ区分","1","1"));
            }

            if (!NUMERIC_HALF_WIDTH_PATTERN.matcher(msgKbn).matches()) {
                throw new ValidationException(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_021, "メッセージ区分","半角数字"));
            }

            AnnounceMessageResponse response = messageApplicationService.getAnnounceMessage(userInfo, msgKbn);

            logger.info(formatMessage(GlobalMessageConstants.INF_002, "共通処理.メッセージ取得", BusinessConstants.SUCCESS_MSG));
            return CommonResult.success(response);

        } catch (Exception e) {
            logger.error("アナウンスメッセージ取得処理中にエラーが発生しました", e);
            logger.info(formatMessage(GlobalMessageConstants.INF_002, "共通処理.メッセージ取得", BusinessConstants.GENERAL_ERROR_MSG));
            return CommonResult.error(50001, e.getMessage());
        }
    }
}