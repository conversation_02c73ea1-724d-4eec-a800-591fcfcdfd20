package com.ms.bp.domain.user;

import com.ms.bp.shared.common.constants.BusinessConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ユーザー業務ドメインサービス
 * ユーザーに関する業務ルールとビジネスロジックを実装
 * DDD原則に従い、純粋な業務規則をドメイン層で管理
 */
public class UserBusinessDomainService {
    private static final Logger logger = LoggerFactory.getLogger(UserBusinessDomainService.class);

    /**
     * エリアコードに基づいて所属区分を判定
     * エリアコードが'0000'の場合は本社、それ以外はエリアとして判定
     * 
     * ビジネスルール：
     * - エリアコード'0000' = 本社（所属区分コード'1'）
     * - その他のエリアコード = エリア（所属区分コード'2'）
     * 
     * @param areaCode エリアコード（4桁）
     * @return 所属区分コード（1:本社 2:エリア）
     * @throws IllegalArgumentException エリアコードがnullまたは空の場合
     */
    public String determineAffiliationDivision(String areaCode) {
        if (areaCode == null || areaCode.trim().isEmpty()) {
            logger.warn("エリアコードが無効です: {}", areaCode);
            throw new IllegalArgumentException("エリアコードが指定されていません");
        }

        if (BusinessConstants.HEAD_OFFICE_AREA_CODE.equals(areaCode)) {
            return BusinessConstants.AFFILIATION_HEAD_OFFICE;
        } else {
            return BusinessConstants.AFFILIATION_AREA;
        }
    }
}
