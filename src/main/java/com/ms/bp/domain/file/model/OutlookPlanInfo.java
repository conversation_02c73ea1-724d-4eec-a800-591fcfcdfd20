package com.ms.bp.domain.file.model;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.validation.annotation.NumericHalfWidth;
import com.ms.bp.shared.common.io.validation.annotation.Range;
import com.ms.bp.shared.common.io.validation.annotation.Required;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.FunctionUtil;
import com.ms.bp.shared.util.RequestContext;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ファイル処理結果値オブジェクト
 * インポート・エクスポート処理の結果を表現する
 */
@Data
public class OutlookPlanInfo implements DatabaseMappable {
	
	private String no;
    private String ikanmotoBusho;
    private String ikansakiBusho;

    /**
     * エリアコード
     * 権限チェック対象
     */
    @Required(fieldName = "ｴﾘｱｺｰﾄﾞ")
    @Range(min = 4, max = 4, fieldName = "ｴﾘｱｺｰﾄﾞ")
    @NumericHalfWidth(fieldName = "ｴﾘｱｺｰﾄﾞ")
    private String areaCode;

    private String areaName;

    /**
     * グループコード
     * 複合主キーの一部
     */
    @Required(fieldName = "ｸﾞﾙｰﾌﾟCD")
    @Range(min = 4, max = 4, fieldName = "ｸﾞﾙｰﾌﾟCD")
    @NumericHalfWidth(fieldName = "ｸﾞﾙｰﾌﾟCD")
    private String groupCode;

    /**
     * ユニットコード
     */
    @Required(fieldName = "ﾕﾆｯﾄCD")
    @Range(min = 5, max = 5, fieldName = "ﾕﾆｯﾄCD")
    @NumericHalfWidth(fieldName = "ﾕﾆｯﾄCD")
    private String unitCode;

    /**
     * 担当者名
     */
    @Range(min = 1, max = 25, fieldName = "担当者")
    private String tantoshaName;

    /**
     * 採算CD7桁
     * 複合主キーの一部
     * マスタにない採算管理単位コードも入力可能
     */
    @Required(fieldName = "採算CD7桁")
    @Range(min = 7, max = 7, fieldName = "採算CD7桁")
    @NumericHalfWidth(fieldName = "採算CD7桁")
    private String saisanCode;

    private String saisanName;

    /**
     * 企業コード
     */
    @Required(fieldName = "企業CD")
    @Range(min = 7, max = 7, fieldName = "企業CD")
    @NumericHalfWidth(fieldName = "企業CD")
    private String kigyoCode;

    private String kigyoName;

    /**
     * カテゴリ
     */
    @Range(min = 1, max = 15, fieldName = "ｶﾃｺﾞﾘ")
    private String kategori;

    /**
     * サブカテ
     */
    @Range(min = 1, max = 15, fieldName = "ｻﾌﾞｶﾃ")
    private String subKategori;

    /**
     * 業態(事業計画)
     */
    @Range(min = 1, max = 30, fieldName = "業態(事業計画)")
    private String gyotai;

    /**
     * 変更後取組区分
     */
    @Range(min = 1, max = 20, fieldName = "変更後取組区分")
    private String afterTorikumKbn;

    /**
     * 業態比率
     */
    //@Digits(integer=3, fraction=2, fieldName = "業態比率")
    private float gyotaiHiritsu;


    // 4月(計画)_総売上高(在庫)
    @Required(fieldName = "4月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_総売上高(在庫)")
    private Long planAprAllSalInven;
    // 4月(計画)_総売上高(直送)
    @Required(fieldName = "4月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_総売上高(直送)")
    private Long planAprAllSalChokusou;
    // 4月(計画)_総売上高計
    //@Required(fieldName = "4月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "4月(計画)_総売上高計")
    //private Long planAprAllSalTotal;
    // 4月(計画)_返品(在庫)
    @Required(fieldName = "4月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_返品(在庫)")
    private Long planAprReturnInven;
    // 4月(計画)_返品(直送)
    @Required(fieldName = "4月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_返品(直送)")
    private Long planAprReturnChokusou;
    // 4月(計画)_返品計
    //@Required(fieldName = "4月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "4月(計画)_返品計")
    //private Long planAprReturnTotal;
    // 4月(計画)_リベート(在庫)
    @Required(fieldName = "4月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_リベート(在庫)")
    private Long planAprRebateInven;
    // 4月(計画)_リベート(直送)
    @Required(fieldName = "4月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_リベート(直送)")
    private Long planAprRebateChokusou;
    // 4月(計画)_リベート計
    //@Required(fieldName = "4月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "4月(計画)_リベート計")
    //private Long planAprRebateTotal;
    // 4月(計画)_センターフィ(在庫)
    @Required(fieldName = "4月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_センターフィ(在庫)")
    private Long planAprCenterFeeInven;
    // 4月(計画)_センターフィ(直送)
    @Required(fieldName = "4月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_センターフィ(直送)")
    private Long planAprCenterFeeChokusou;
    // 4月(計画)_センターフィ計
    //@Required(fieldName = "4月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "4月(計画)_センターフィ計")
    //private Long planAprCenterFeeTotal;
    // 4月(計画)_直接利益(在庫)
    @Required(fieldName = "4月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_直接利益(在庫)")
    private Long planAprChokuRiekiInven;
    // 4月(計画)_直接利益(直送)
    @Required(fieldName = "4月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_直接利益(直送)")
    private Long planAprChokuRiekiChokusou;
    // 4月(計画)_直接利益計
    //@Required(fieldName = "4月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "4月(計画)_直接利益計")
    //private Long planAprChokuRiekiTotal;
    // 4月(計画)_直利率(在庫)
    //@Required(fieldName = "4月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "4月(計画)_直利率(在庫)")
    //private Long planAprChokuRateInven;
    // 4月(計画)_直利率(直送)
    //@Required(fieldName = "4月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "4月(計画)_直利率(直送)")
    //private Long planAprChokuRateChokusou;
    // 4月(計画)_直利率計
    //@Required(fieldName = "4月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "4月(計画)_直利率計")
    //private Long planAprChokuRateTotal;
    // 5月(計画)_総売上高(在庫)
    @Required(fieldName = "5月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_総売上高(在庫)")
    private Long planMayAllSalInven;
    // 5月(計画)_総売上高(直送)
    @Required(fieldName = "5月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_総売上高(直送)")
    private Long planMayAllSalChokusou;
    // 5月(計画)_総売上高計
    //@Required(fieldName = "5月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "5月(計画)_総売上高計")
    //private Long planMayAllSalTotal;
    // 5月(計画)_返品(在庫)
    @Required(fieldName = "5月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_返品(在庫)")
    private Long planMayReturnInven;
    // 5月(計画)_返品(直送)
    @Required(fieldName = "5月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_返品(直送)")
    private Long planMayReturnChokusou;
    // 5月(計画)_返品計
    //@Required(fieldName = "5月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "5月(計画)_返品計")
    //private Long planMayReturnTotal;
    // 5月(計画)_リベート(在庫)
    @Required(fieldName = "5月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_リベート(在庫)")
    private Long planMayRebateInven;
    // 5月(計画)_リベート(直送)
    @Required(fieldName = "5月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_リベート(直送)")
    private Long planMayRebateChokusou;
    // 5月(計画)_リベート計
    //@Required(fieldName = "5月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "5月(計画)_リベート計")
    //private Long planMayRebateTotal;
    // 5月(計画)_センターフィ(在庫)
    @Required(fieldName = "5月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_センターフィ(在庫)")
    private Long planMayCenterFeeInven;
    // 5月(計画)_センターフィ(直送)
    @Required(fieldName = "5月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_センターフィ(直送)")
    private Long planMayCenterFeeChokusou;
    // 5月(計画)_センターフィ計
    //@Required(fieldName = "5月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "5月(計画)_センターフィ計")
    //private Long planMayCenterFeeTotal;
    // 5月(計画)_直接利益(在庫)
    @Required(fieldName = "5月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_直接利益(在庫)")
    private Long planMayChokuRiekiInven;
    // 5月(計画)_直接利益(直送)
    @Required(fieldName = "5月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_直接利益(直送)")
    private Long planMayChokuRiekiChokusou;
    // 5月(計画)_直接利益計
    //@Required(fieldName = "5月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "5月(計画)_直接利益計")
    //private Long planMayChokuRiekiTotal;
    // 5月(計画)_直利率(在庫)
    //@Required(fieldName = "5月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "5月(計画)_直利率(在庫)")
    //private Long planMayChokuRateInven;
    // 5月(計画)_直利率(直送)
    //@Required(fieldName = "5月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "5月(計画)_直利率(直送)")
    //private Long planMayChokuRateChokusou;
    // 5月(計画)_直利率計
    //@Required(fieldName = "5月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "5月(計画)_直利率計")
    //private Long planMayChokuRateTotal;
    // 6月(計画)_総売上高(在庫)
    @Required(fieldName = "6月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_総売上高(在庫)")
    private Long planJunAllSalInven;
    // 6月(計画)_総売上高(直送)
    @Required(fieldName = "6月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_総売上高(直送)")
    private Long planJunAllSalChokusou;
    // 6月(計画)_総売上高計
    //@Required(fieldName = "6月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "6月(計画)_総売上高計")
    //private Long planJunAllSalTotal;
    // 6月(計画)_返品(在庫)
    @Required(fieldName = "6月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_返品(在庫)")
    private Long planJunReturnInven;
    // 6月(計画)_返品(直送)
    @Required(fieldName = "6月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_返品(直送)")
    private Long planJunReturnChokusou;
    // 6月(計画)_返品計
    //@Required(fieldName = "6月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "6月(計画)_返品計")
    //private Long planJunReturnTotal;
    // 6月(計画)_リベート(在庫)
    @Required(fieldName = "6月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_リベート(在庫)")
    private Long planJunRebateInven;
    // 6月(計画)_リベート(直送)
    @Required(fieldName = "6月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_リベート(直送)")
    private Long planJunRebateChokusou;
    // 6月(計画)_リベート計
    //@Required(fieldName = "6月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "6月(計画)_リベート計")
    //private Long planJunRebateTotal;
    // 6月(計画)_センターフィ(在庫)
    @Required(fieldName = "6月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_センターフィ(在庫)")
    private Long planJunCenterFeeInven;
    // 6月(計画)_センターフィ(直送)
    @Required(fieldName = "6月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_センターフィ(直送)")
    private Long planJunCenterFeeChokusou;
    // 6月(計画)_センターフィ計
    //@Required(fieldName = "6月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "6月(計画)_センターフィ計")
    //private Long planJunCenterFeeTotal;
    // 6月(計画)_直接利益(在庫)
    @Required(fieldName = "6月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_直接利益(在庫)")
    private Long planJunChokuRiekiInven;
    // 6月(計画)_直接利益(直送)
    @Required(fieldName = "6月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_直接利益(直送)")
    private Long planJunChokuRiekiChokusou;
    // 6月(計画)_直接利益計
    //@Required(fieldName = "6月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "6月(計画)_直接利益計")
    //private Long planJunChokuRiekiTotal;
    // 6月(計画)_直利率(在庫)
    //@Required(fieldName = "6月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "6月(計画)_直利率(在庫)")
    //private Long planJunChokuRateInven;
    // 6月(計画)_直利率(直送)
    //@Required(fieldName = "6月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "6月(計画)_直利率(直送)")
    //private Long planJunChokuRateChokusou;
    // 6月(計画)_直利率計
    //@Required(fieldName = "6月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "6月(計画)_直利率計")
    //private Long planJunChokuRateTotal;
    // 7月(計画)_総売上高(在庫)
    @Required(fieldName = "7月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_総売上高(在庫)")
    private Long planJulAllSalInven;
    // 7月(計画)_総売上高(直送)
    @Required(fieldName = "7月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_総売上高(直送)")
    private Long planJulAllSalChokusou;
    // 7月(計画)_総売上高計
    //@Required(fieldName = "7月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "7月(計画)_総売上高計")
    //private Long planJulAllSalTotal;
    // 7月(計画)_返品(在庫)
    @Required(fieldName = "7月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_返品(在庫)")
    private Long planJulReturnInven;
    // 7月(計画)_返品(直送)
    @Required(fieldName = "7月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_返品(直送)")
    private Long planJulReturnChokusou;
    // 7月(計画)_返品計
    //@Required(fieldName = "7月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "7月(計画)_返品計")
    //private Long planJulReturnTotal;
    // 7月(計画)_リベート(在庫)
    @Required(fieldName = "7月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_リベート(在庫)")
    private Long planJulRebateInven;
    // 7月(計画)_リベート(直送)
    @Required(fieldName = "7月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_リベート(直送)")
    private Long planJulRebateChokusou;
    // 7月(計画)_リベート計
    //@Required(fieldName = "7月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "7月(計画)_リベート計")
    //private Long planJulRebateTotal;
    // 7月(計画)_センターフィ(在庫)
    @Required(fieldName = "7月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_センターフィ(在庫)")
    private Long planJulCenterFeeInven;
    // 7月(計画)_センターフィ(直送)
    @Required(fieldName = "7月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_センターフィ(直送)")
    private Long planJulCenterFeeChokusou;
    // 7月(計画)_センターフィ計
    //@Required(fieldName = "7月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "7月(計画)_センターフィ計")
    //private Long planJulCenterFeeTotal;
    // 7月(計画)_直接利益(在庫)
    @Required(fieldName = "7月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_直接利益(在庫)")
    private Long planJulChokuRiekiInven;
    // 7月(計画)_直接利益(直送)
    @Required(fieldName = "7月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_直接利益(直送)")
    private Long planJulChokuRiekiChokusou;
    // 7月(計画)_直接利益計
    //@Required(fieldName = "7月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "7月(計画)_直接利益計")
    //private Long planJulChokuRiekiTotal;
    // 7月(計画)_直利率(在庫)
    //@Required(fieldName = "7月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "7月(計画)_直利率(在庫)")
    //private Long planJulChokuRateInven;
    // 7月(計画)_直利率(直送)
    //@Required(fieldName = "7月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "7月(計画)_直利率(直送)")
    //private Long planJulChokuRateChokusou;
    // 7月(計画)_直利率計
    //@Required(fieldName = "7月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "7月(計画)_直利率計")
    //private Long planJulChokuRateTotal;
    // 8月(計画)_総売上高(在庫)
    @Required(fieldName = "8月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_総売上高(在庫)")
    private Long planAugAllSalInven;
    // 8月(計画)_総売上高(直送)
    @Required(fieldName = "8月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_総売上高(直送)")
    private Long planAugAllSalChokusou;
    // 8月(計画)_総売上高計
    //@Required(fieldName = "8月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "8月(計画)_総売上高計")
    //private Long planAugAllSalTotal;
    // 8月(計画)_返品(在庫)
    @Required(fieldName = "8月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_返品(在庫)")
    private Long planAugReturnInven;
    // 8月(計画)_返品(直送)
    @Required(fieldName = "8月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_返品(直送)")
    private Long planAugReturnChokusou;
    // 8月(計画)_返品計
    //@Required(fieldName = "8月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "8月(計画)_返品計")
    //private Long planAugReturnTotal;
    // 8月(計画)_リベート(在庫)
    @Required(fieldName = "8月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_リベート(在庫)")
    private Long planAugRebateInven;
    // 8月(計画)_リベート(直送)
    @Required(fieldName = "8月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_リベート(直送)")
    private Long planAugRebateChokusou;
    // 8月(計画)_リベート計
    //@Required(fieldName = "8月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "8月(計画)_リベート計")
    //private Long planAugRebateTotal;
    // 8月(計画)_センターフィ(在庫)
    @Required(fieldName = "8月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_センターフィ(在庫)")
    private Long planAugCenterFeeInven;
    // 8月(計画)_センターフィ(直送)
    @Required(fieldName = "8月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_センターフィ(直送)")
    private Long planAugCenterFeeChokusou;
    // 8月(計画)_センターフィ計
    //@Required(fieldName = "8月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "8月(計画)_センターフィ計")
    //private Long planAugCenterFeeTotal;
    // 8月(計画)_直接利益(在庫)
    @Required(fieldName = "8月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_直接利益(在庫)")
    private Long planAugChokuRiekiInven;
    // 8月(計画)_直接利益(直送)
    @Required(fieldName = "8月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_直接利益(直送)")
    private Long planAugChokuRiekiChokusou;
    // 8月(計画)_直接利益計
    //@Required(fieldName = "8月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "8月(計画)_直接利益計")
    //private Long planAugChokuRiekiTotal;
    // 8月(計画)_直利率(在庫)
    //@Required(fieldName = "8月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "8月(計画)_直利率(在庫)")
    //private Long planAugChokuRateInven;
    // 8月(計画)_直利率(直送)
    //@Required(fieldName = "8月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "8月(計画)_直利率(直送)")
    //private Long planAugChokuRateChokusou;
    // 8月(計画)_直利率計
    //@Required(fieldName = "8月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "8月(計画)_直利率計")
    //private Long planAugChokuRateTotal;
    // 9月(計画)_総売上高(在庫)
    @Required(fieldName = "9月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_総売上高(在庫)")
    private Long planSepAllSalInven;
    // 9月(計画)_総売上高(直送)
    @Required(fieldName = "9月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_総売上高(直送)")
    private Long planSepAllSalChokusou;
    // 9月(計画)_総売上高計
    //@Required(fieldName = "9月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "9月(計画)_総売上高計")
    //private Long planSepAllSalTotal;
    // 9月(計画)_返品(在庫)
    @Required(fieldName = "9月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_返品(在庫)")
    private Long planSepReturnInven;
    // 9月(計画)_返品(直送)
    @Required(fieldName = "9月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_返品(直送)")
    private Long planSepReturnChokusou;
    // 9月(計画)_返品計
    //@Required(fieldName = "9月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "9月(計画)_返品計")
    //private Long planSepReturnTotal;
    // 9月(計画)_リベート(在庫)
    @Required(fieldName = "9月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_リベート(在庫)")
    private Long planSepRebateInven;
    // 9月(計画)_リベート(直送)
    @Required(fieldName = "9月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_リベート(直送)")
    private Long planSepRebateChokusou;
    // 9月(計画)_リベート計
    //@Required(fieldName = "9月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "9月(計画)_リベート計")
    //private Long planSepRebateTotal;
    // 9月(計画)_センターフィ(在庫)
    @Required(fieldName = "9月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_センターフィ(在庫)")
    private Long planSepCenterFeeInven;
    // 9月(計画)_センターフィ(直送)
    @Required(fieldName = "9月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_センターフィ(直送)")
    private Long planSepCenterFeeChokusou;
    // 9月(計画)_センターフィ計
    //@Required(fieldName = "9月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "9月(計画)_センターフィ計")
    //private Long planSepCenterFeeTotal;
    // 9月(計画)_直接利益(在庫)
    @Required(fieldName = "9月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_直接利益(在庫)")
    private Long planSepChokuRiekiInven;
    // 9月(計画)_直接利益(直送)
    @Required(fieldName = "9月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_直接利益(直送)")
    private Long planSepChokuRiekiChokusou;
    // 9月(計画)_直接利益計
    //@Required(fieldName = "9月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "9月(計画)_直接利益計")
    //private Long planSepChokuRiekiTotal;
    // 9月(計画)_直利率(在庫)
    //@Required(fieldName = "9月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "9月(計画)_直利率(在庫)")
    //private Long planSepChokuRateInven;
    // 9月(計画)_直利率(直送)
    //@Required(fieldName = "9月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "9月(計画)_直利率(直送)")
    //private Long planSepChokuRateChokusou;
    // 9月(計画)_直利率計
    //@Required(fieldName = "9月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "9月(計画)_直利率計")
    //private Long planSepChokuRateTotal;
    // 10月(計画)_総売上高(在庫)
    @Required(fieldName = "10月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_総売上高(在庫)")
    private Long planOctAllSalInven;
    // 10月(計画)_総売上高(直送)
    @Required(fieldName = "10月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_総売上高(直送)")
    private Long planOctAllSalChokusou;
    // 10月(計画)_総売上高計
    //@Required(fieldName = "10月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "10月(計画)_総売上高計")
    //private Long planOctAllSalTotal;
    // 10月(計画)_返品(在庫)
    @Required(fieldName = "10月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_返品(在庫)")
    private Long planOctReturnInven;
    // 10月(計画)_返品(直送)
    @Required(fieldName = "10月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_返品(直送)")
    private Long planOctReturnChokusou;
    // 10月(計画)_返品計
    //@Required(fieldName = "10月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "10月(計画)_返品計")
    //private Long planOctReturnTotal;
    // 10月(計画)_リベート(在庫)
    @Required(fieldName = "10月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_リベート(在庫)")
    private Long planOctRebateInven;
    // 10月(計画)_リベート(直送)
    @Required(fieldName = "10月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_リベート(直送)")
    private Long planOctRebateChokusou;
    // 10月(計画)_リベート計
    //@Required(fieldName = "10月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "10月(計画)_リベート計")
    //private Long planOctRebateTotal;
    // 10月(計画)_センターフィ(在庫)
    @Required(fieldName = "10月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_センターフィ(在庫)")
    private Long planOctCenterFeeInven;
    // 10月(計画)_センターフィ(直送)
    @Required(fieldName = "10月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_センターフィ(直送)")
    private Long planOctCenterFeeChokusou;
    // 10月(計画)_センターフィ計
    //@Required(fieldName = "10月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "10月(計画)_センターフィ計")
    //private Long planOctCenterFeeTotal;
    // 10月(計画)_直接利益(在庫)
    @Required(fieldName = "10月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_直接利益(在庫)")
    private Long planOctChokuRiekiInven;
    // 10月(計画)_直接利益(直送)
    @Required(fieldName = "10月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_直接利益(直送)")
    private Long planOctChokuRiekiChokusou;
    // 10月(計画)_直接利益計
    //@Required(fieldName = "10月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "10月(計画)_直接利益計")
    //private Long planOctChokuRiekiTotal;
    // 10月(計画)_直利率(在庫)
    //@Required(fieldName = "10月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "10月(計画)_直利率(在庫)")
    //private Long planOctChokuRateInven;
    // 10月(計画)_直利率(直送)
    //@Required(fieldName = "10月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "10月(計画)_直利率(直送)")
    //private Long planOctChokuRateChokusou;
    // 10月(計画)_直利率計
    //@Required(fieldName = "10月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "10月(計画)_直利率計")
    //private Long planOctChokuRateTotal;
    // 11月(計画)_総売上高(在庫)
    @Required(fieldName = "11月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_総売上高(在庫)")
    private Long planNovAllSalInven;
    // 11月(計画)_総売上高(直送)
    @Required(fieldName = "11月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_総売上高(直送)")
    private Long planNovAllSalChokusou;
    // 11月(計画)_総売上高計
    //@Required(fieldName = "11月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "11月(計画)_総売上高計")
    //private Long planNovAllSalTotal;
    // 11月(計画)_返品(在庫)
    @Required(fieldName = "11月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_返品(在庫)")
    private Long planNovReturnInven;
    // 11月(計画)_返品(直送)
    @Required(fieldName = "11月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_返品(直送)")
    private Long planNovReturnChokusou;
    // 11月(計画)_返品計
    //@Required(fieldName = "11月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "11月(計画)_返品計")
    //private Long planNovReturnTotal;
    // 11月(計画)_リベート(在庫)
    @Required(fieldName = "11月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_リベート(在庫)")
    private Long planNovRebateInven;
    // 11月(計画)_リベート(直送)
    @Required(fieldName = "11月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_リベート(直送)")
    private Long planNovRebateChokusou;
    // 11月(計画)_リベート計
    //@Required(fieldName = "11月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "11月(計画)_リベート計")
    //private Long planNovRebateTotal;
    // 11月(計画)_センターフィ(在庫)
    @Required(fieldName = "11月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_センターフィ(在庫)")
    private Long planNovCenterFeeInven;
    // 11月(計画)_センターフィ(直送)
    @Required(fieldName = "11月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_センターフィ(直送)")
    private Long planNovCenterFeeChokusou;
    // 11月(計画)_センターフィ計
    //@Required(fieldName = "11月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "11月(計画)_センターフィ計")
    //private Long planNovCenterFeeTotal;
    // 11月(計画)_直接利益(在庫)
    @Required(fieldName = "11月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_直接利益(在庫)")
    private Long planNovChokuRiekiInven;
    // 11月(計画)_直接利益(直送)
    @Required(fieldName = "11月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_直接利益(直送)")
    private Long planNovChokuRiekiChokusou;
    // 11月(計画)_直接利益計
    //@Required(fieldName = "11月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "11月(計画)_直接利益計")
    //private Long planNovChokuRiekiTotal;
    // 11月(計画)_直利率(在庫)
    //@Required(fieldName = "11月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "11月(計画)_直利率(在庫)")
    //private Long planNovChokuRateInven;
    // 11月(計画)_直利率(直送)
    //@Required(fieldName = "11月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "11月(計画)_直利率(直送)")
    //private Long planNovChokuRateChokusou;
    // 11月(計画)_直利率計
    //@Required(fieldName = "11月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "11月(計画)_直利率計")
    //private Long planNovChokuRateTotal;
    // 12月(計画)_総売上高(在庫)
    @Required(fieldName = "12月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_総売上高(在庫)")
    private Long planDecAllSalInven;
    // 12月(計画)_総売上高(直送)
    @Required(fieldName = "12月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_総売上高(直送)")
    private Long planDecAllSalChokusou;
    // 12月(計画)_総売上高計
    //@Required(fieldName = "12月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "12月(計画)_総売上高計")
    //private Long planDecAllSalTotal;
    // 12月(計画)_返品(在庫)
    @Required(fieldName = "12月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_返品(在庫)")
    private Long planDecReturnInven;
    // 12月(計画)_返品(直送)
    @Required(fieldName = "12月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_返品(直送)")
    private Long planDecReturnChokusou;
    // 12月(計画)_返品計
    //@Required(fieldName = "12月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "12月(計画)_返品計")
    //private Long planDecReturnTotal;
    // 12月(計画)_リベート(在庫)
    @Required(fieldName = "12月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_リベート(在庫)")
    private Long planDecRebateInven;
    // 12月(計画)_リベート(直送)
    @Required(fieldName = "12月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_リベート(直送)")
    private Long planDecRebateChokusou;
    // 12月(計画)_リベート計
    //@Required(fieldName = "12月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "12月(計画)_リベート計")
    //private Long planDecRebateTotal;
    // 12月(計画)_センターフィ(在庫)
    @Required(fieldName = "12月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_センターフィ(在庫)")
    private Long planDecCenterFeeInven;
    // 12月(計画)_センターフィ(直送)
    @Required(fieldName = "12月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_センターフィ(直送)")
    private Long planDecCenterFeeChokusou;
    // 12月(計画)_センターフィ計
    //@Required(fieldName = "12月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "12月(計画)_センターフィ計")
    //private Long planDecCenterFeeTotal;
    // 12月(計画)_直接利益(在庫)
    @Required(fieldName = "12月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_直接利益(在庫)")
    private Long planDecChokuRiekiInven;
    // 12月(計画)_直接利益(直送)
    @Required(fieldName = "12月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_直接利益(直送)")
    private Long planDecChokuRiekiChokusou;
    // 12月(計画)_直接利益計
    //@Required(fieldName = "12月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "12月(計画)_直接利益計")
    //private Long planDecChokuRiekiTotal;
    // 12月(計画)_直利率(在庫)
    //@Required(fieldName = "12月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "12月(計画)_直利率(在庫)")
    //private Long planDecChokuRateInven;
    // 12月(計画)_直利率(直送)
    //@Required(fieldName = "12月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "12月(計画)_直利率(直送)")
    //private Long planDecChokuRateChokusou;
    // 12月(計画)_直利率計
    //@Required(fieldName = "12月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "12月(計画)_直利率計")
    //private Long planDecChokuRateTotal;
    // 1月(計画)_総売上高(在庫)
    @Required(fieldName = "1月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_総売上高(在庫)")
    private Long planJanAllSalInven;
    // 1月(計画)_総売上高(直送)
    @Required(fieldName = "1月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_総売上高(直送)")
    private Long planJanAllSalChokusou;
    // 1月(計画)_総売上高計
    //@Required(fieldName = "1月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "1月(計画)_総売上高計")
    //private Long planJanAllSalTotal;
    // 1月(計画)_返品(在庫)
    @Required(fieldName = "1月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_返品(在庫)")
    private Long planJanReturnInven;
    // 1月(計画)_返品(直送)
    @Required(fieldName = "1月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_返品(直送)")
    private Long planJanReturnChokusou;
    // 1月(計画)_返品計
    //@Required(fieldName = "1月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "1月(計画)_返品計")
    //private Long planJanReturnTotal;
    // 1月(計画)_リベート(在庫)
    @Required(fieldName = "1月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_リベート(在庫)")
    private Long planJanRebateInven;
    // 1月(計画)_リベート(直送)
    @Required(fieldName = "1月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_リベート(直送)")
    private Long planJanRebateChokusou;
    // 1月(計画)_リベート計
    //@Required(fieldName = "1月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "1月(計画)_リベート計")
    //private Long planJanRebateTotal;
    // 1月(計画)_センターフィ(在庫)
    @Required(fieldName = "1月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_センターフィ(在庫)")
    private Long planJanCenterFeeInven;
    // 1月(計画)_センターフィ(直送)
    @Required(fieldName = "1月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_センターフィ(直送)")
    private Long planJanCenterFeeChokusou;
    // 1月(計画)_センターフィ計
    //@Required(fieldName = "1月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "1月(計画)_センターフィ計")
    //private Long planJanCenterFeeTotal;
    // 1月(計画)_直接利益(在庫)
    @Required(fieldName = "1月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_直接利益(在庫)")
    private Long planJanChokuRiekiInven;
    // 1月(計画)_直接利益(直送)
    @Required(fieldName = "1月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_直接利益(直送)")
    private Long planJanChokuRiekiChokusou;
    // 1月(計画)_直接利益計
    //@Required(fieldName = "1月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "1月(計画)_直接利益計")
    //private Long planJanChokuRiekiTotal;
    // 1月(計画)_直利率(在庫)
    //@Required(fieldName = "1月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "1月(計画)_直利率(在庫)")
    //private Long planJanChokuRateInven;
    // 1月(計画)_直利率(直送)
    //@Required(fieldName = "1月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "1月(計画)_直利率(直送)")
    //private Long planJanChokuRateChokusou;
    // 1月(計画)_直利率計
    //@Required(fieldName = "1月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "1月(計画)_直利率計")
    //private Long planJanChokuRateTotal;
    // 2月(計画)_総売上高(在庫)
    @Required(fieldName = "2月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_総売上高(在庫)")
    private Long planFebAllSalInven;
    // 2月(計画)_総売上高(直送)
    @Required(fieldName = "2月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_総売上高(直送)")
    private Long planFebAllSalChokusou;
    // 2月(計画)_総売上高計
    //@Required(fieldName = "2月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "2月(計画)_総売上高計")
    //private Long planFebAllSalTotal;
    // 2月(計画)_返品(在庫)
    @Required(fieldName = "2月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_返品(在庫)")
    private Long planFebReturnInven;
    // 2月(計画)_返品(直送)
    @Required(fieldName = "2月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_返品(直送)")
    private Long planFebReturnChokusou;
    // 2月(計画)_返品計
    //@Required(fieldName = "2月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "2月(計画)_返品計")
    //private Long planFebReturnTotal;
    // 2月(計画)_リベート(在庫)
    @Required(fieldName = "2月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_リベート(在庫)")
    private Long planFebRebateInven;
    // 2月(計画)_リベート(直送)
    @Required(fieldName = "2月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_リベート(直送)")
    private Long planFebRebateChokusou;
    // 2月(計画)_リベート計
    //@Required(fieldName = "2月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "2月(計画)_リベート計")
    //private Long planFebRebateTotal;
    // 2月(計画)_センターフィ(在庫)
    @Required(fieldName = "2月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_センターフィ(在庫)")
    private Long planFebCenterFeeInven;
    // 2月(計画)_センターフィ(直送)
    @Required(fieldName = "2月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_センターフィ(直送)")
    private Long planFebCenterFeeChokusou;
    // 2月(計画)_センターフィ計
    //@Required(fieldName = "2月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "2月(計画)_センターフィ計")
    //private Long planFebCenterFeeTotal;
    // 2月(計画)_直接利益(在庫)
    @Required(fieldName = "2月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_直接利益(在庫)")
    private Long planFebChokuRiekiInven;
    // 2月(計画)_直接利益(直送)
    @Required(fieldName = "2月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_直接利益(直送)")
    private Long planFebChokuRiekiChokusou;
    // 2月(計画)_直接利益計
    //@Required(fieldName = "2月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "2月(計画)_直接利益計")
    //private Long planFebChokuRiekiTotal;
    // 2月(計画)_直利率(在庫)
    //@Required(fieldName = "2月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "2月(計画)_直利率(在庫)")
    //private Long planFebChokuRateInven;
    // 2月(計画)_直利率(直送)
    //@Required(fieldName = "2月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "2月(計画)_直利率(直送)")
    //private Long planFebChokuRateChokusou;
    // 2月(計画)_直利率計
    //@Required(fieldName = "2月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "2月(計画)_直利率計")
    //private Long planFebChokuRateTotal;
    // 3月(計画)_総売上高(在庫)
    @Required(fieldName = "3月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_総売上高(在庫)")
    private Long planMarAllSalInven;
    // 3月(計画)_総売上高(直送)
    @Required(fieldName = "3月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_総売上高(直送)")
    private Long planMarAllSalChokusou;
    // 3月(計画)_総売上高計
    //@Required(fieldName = "3月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "3月(計画)_総売上高計")
    //private Long planMarAllSalTotal;
    // 3月(計画)_返品(在庫)
    @Required(fieldName = "3月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_返品(在庫)")
    private Long planMarReturnInven;
    // 3月(計画)_返品(直送)
    @Required(fieldName = "3月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_返品(直送)")
    private Long planMarReturnChokusou;
    // 3月(計画)_返品計
    //@Required(fieldName = "3月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "3月(計画)_返品計")
    //private Long planMarReturnTotal;
    // 3月(計画)_リベート(在庫)
    @Required(fieldName = "3月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_リベート(在庫)")
    private Long planMarRebateInven;
    // 3月(計画)_リベート(直送)
    @Required(fieldName = "3月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_リベート(直送)")
    private Long planMarRebateChokusou;
    // 3月(計画)_リベート計
    //@Required(fieldName = "3月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "3月(計画)_リベート計")
    //private Long planMarRebateTotal;
    // 3月(計画)_センターフィ(在庫)
    @Required(fieldName = "3月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_センターフィ(在庫)")
    private Long planMarCenterFeeInven;
    // 3月(計画)_センターフィ(直送)
    @Required(fieldName = "3月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_センターフィ(直送)")
    private Long planMarCenterFeeChokusou;
    // 3月(計画)_センターフィ計
    //@Required(fieldName = "3月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "3月(計画)_センターフィ計")
    //private Long planMarCenterFeeTotal;
    // 3月(計画)_直接利益(在庫)
    @Required(fieldName = "3月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_直接利益(在庫)")
    private Long planMarChokuRiekiInven;
    // 3月(計画)_直接利益(直送)
    @Required(fieldName = "3月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_直接利益(直送)")
    private Long planMarChokuRiekiChokusou;
    // 3月(計画)_直接利益計
    //@Required(fieldName = "3月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "3月(計画)_直接利益計")
    //private Long planMarChokuRiekiTotal;
    // 3月(計画)_直利率(在庫)
    //@Required(fieldName = "3月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "3月(計画)_直利率(在庫)")
    //private Long planMarChokuRateInven;
    // 3月(計画)_直利率(直送)
    //@Required(fieldName = "3月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "3月(計画)_直利率(直送)")
    //private Long planMarChokuRateChokusou;
    // 3月(計画)_直利率計
    //@Required(fieldName = "3月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "3月(計画)_直利率計")
    //private Long planMarChokuRateTotal;



    // 4月(実績見通し)_総売上高(在庫)
    private Long outlookAprAllSalInven;
    // 4月(実績見通し)_総売上高(直送)
    private Long outlookAprAllSalChokusou;
    // 4月(実績見通し)_総売上高計
    //private Long outlookAprAllSalTotal;
    // 4月(実績見通し)_返品(在庫)
    private Long outlookAprReturnInven;
    // 4月(実績見通し)_返品(直送)
    private Long outlookAprReturnChokusou;
    // 4月(実績見通し)_返品計
    //private Long outlookAprReturnTotal;
    // 4月(実績見通し)_リベート(在庫)
    private Long outlookAprRebateInven;
    // 4月(実績見通し)_リベート(直送)
    private Long outlookAprRebateChokusou;
    // 4月(実績見通し)_リベート計
    //private Long outlookAprRebateTotal;
    // 4月(実績見通し)_センターフィ(在庫)
    private Long outlookAprCenterFeeInven;
    // 4月(実績見通し)_センターフィ(直送)
    private Long outlookAprCenterFeeChokusou;
    // 4月(実績見通し)_センターフィ計
    //private Long outlookAprCenterFeeTotal;
    // 4月(実績見通し)_直接利益(在庫)
    private Long outlookAprChokuRiekiInven;
    // 4月(実績見通し)_直接利益(直送)
    private Long outlookAprChokuRiekiChokusou;
    // 4月(実績見通し)_直接利益計
    //private Long outlookAprChokuRiekiTotal;
    // 4月(実績見通し)_直利率(在庫)
    //private Long outlookAprChokuRateInven;
    // 4月(実績見通し)_直利率(直送)
    //private Long outlookAprChokuRateChokusou;
    // 4月(実績見通し)_直利率計
    //private Long outlookAprChokuRateTotal;

    // 5月(実績見通し)_総売上高(在庫)
    private Long outlookMayAllSalInven;
    // 5月(実績見通し)_総売上高(直送)
    private Long outlookMayAllSalChokusou;
    // 5月(実績見通し)_総売上高計
    //private Long outlookMayAllSalTotal;
    // 5月(実績見通し)_返品(在庫)
    private Long outlookMayReturnInven;
    // 5月(実績見通し)_返品(直送)
    private Long outlookMayReturnChokusou;
    // 5月(実績見通し)_返品計
    //private Long outlookMayReturnTotal;
    // 5月(実績見通し)_リベート(在庫)
    private Long outlookMayRebateInven;
    // 5月(実績見通し)_リベート(直送)
    private Long outlookMayRebateChokusou;
    // 5月(実績見通し)_リベート計
    //private Long outlookMayRebateTotal;
    // 5月(実績見通し)_センターフィ(在庫)
    private Long outlookMayCenterFeeInven;
    // 5月(実績見通し)_センターフィ(直送)
    private Long outlookMayCenterFeeChokusou;
    // 5月(実績見通し)_センターフィ計
    //private Long outlookMayCenterFeeTotal;
    // 5月(実績見通し)_直接利益(在庫)
    private Long outlookMayChokuRiekiInven;
    // 5月(実績見通し)_直接利益(直送)
    private Long outlookMayChokuRiekiChokusou;
    // 5月(実績見通し)_直接利益計
    //private Long outlookMayChokuRiekiTotal;
    // 5月(実績見通し)_直利率(在庫)
    //private Long outlookMayChokuRateInven;
    // 5月(実績見通し)_直利率(直送)
    //private Long outlookMayChokuRateChokusou;
    // 5月(実績見通し)_直利率計
    //private Long outlookMayChokuRateTotal;

    // 6月(実績見通し)_総売上高(在庫)
    private Long outlookJunAllSalInven;
    // 6月(実績見通し)_総売上高(直送)
    private Long outlookJunAllSalChokusou;
    // 6月(実績見通し)_総売上高計
    //private Long outlookJunAllSalTotal;
    // 6月(実績見通し)_返品(在庫)
    private Long outlookJunReturnInven;
    // 6月(実績見通し)_返品(直送)
    private Long outlookJunReturnChokusou;
    // 6月(実績見通し)_返品計
    //private Long outlookJunReturnTotal;
    // 6月(実績見通し)_リベート(在庫)
    private Long outlookJunRebateInven;
    // 6月(実績見通し)_リベート(直送)
    private Long outlookJunRebateChokusou;
    // 6月(実績見通し)_リベート計
    //private Long outlookJunRebateTotal;
    // 6月(実績見通し)_センターフィ(在庫)
    private Long outlookJunCenterFeeInven;
    // 6月(実績見通し)_センターフィ(直送)
    private Long outlookJunCenterFeeChokusou;
    // 6月(実績見通し)_センターフィ計
    //private Long outlookJunCenterFeeTotal;
    // 6月(実績見通し)_直接利益(在庫)
    private Long outlookJunChokuRiekiInven;
    // 6月(実績見通し)_直接利益(直送)
    private Long outlookJunChokuRiekiChokusou;
    // 6月(実績見通し)_直接利益計
    //private Long outlookJunChokuRiekiTotal;
    // 6月(実績見通し)_直利率(在庫)
    //private Long outlookJunChokuRateInven;
    // 6月(実績見通し)_直利率(直送)
    //private Long outlookJunChokuRateChokusou;
    // 6月(実績見通し)_直利率計
    //private Long outlookJunChokuRateTotal;

    // 7月(実績見通し)_総売上高(在庫)
    private Long outlookJulAllSalInven;
    // 7月(実績見通し)_総売上高(直送)
    private Long outlookJulAllSalChokusou;
    // 7月(実績見通し)_総売上高計
    //private Long outlookJulAllSalTotal;
    // 7月(実績見通し)_返品(在庫)
    private Long outlookJulReturnInven;
    // 7月(実績見通し)_返品(直送)
    private Long outlookJulReturnChokusou;
    // 7月(実績見通し)_返品計
    //private Long outlookJulReturnTotal;
    // 7月(実績見通し)_リベート(在庫)
    private Long outlookJulRebateInven;
    // 7月(実績見通し)_リベート(直送)
    private Long outlookJulRebateChokusou;
    // 7月(実績見通し)_リベート計
    //private Long outlookJulRebateTotal;
    // 7月(実績見通し)_センターフィ(在庫)
    private Long outlookJulCenterFeeInven;
    // 7月(実績見通し)_センターフィ(直送)
    private Long outlookJulCenterFeeChokusou;
    // 7月(実績見通し)_センターフィ計
    //private Long outlookJulCenterFeeTotal;
    // 7月(実績見通し)_直接利益(在庫)
    private Long outlookJulChokuRiekiInven;
    // 7月(実績見通し)_直接利益(直送)
    private Long outlookJulChokuRiekiChokusou;
    // 7月(実績見通し)_直接利益計
    //private Long outlookJulChokuRiekiTotal;
    // 7月(実績見通し)_直利率(在庫)
    //private Long outlookJulChokuRateInven;
    // 7月(実績見通し)_直利率(直送)
    //private Long outlookJulChokuRateChokusou;
    // 7月(実績見通し)_直利率計
    //private Long outlookJulChokuRateTotal;

    // 8月(実績見通し)_総売上高(在庫)
    private Long outlookAugAllSalInven;
    // 8月(実績見通し)_総売上高(直送)
    private Long outlookAugAllSalChokusou;
    // 8月(実績見通し)_総売上高計
    //private Long outlookAugAllSalTotal;
    // 8月(実績見通し)_返品(在庫)
    private Long outlookAugReturnInven;
    // 8月(実績見通し)_返品(直送)
    private Long outlookAugReturnChokusou;
    // 8月(実績見通し)_返品計
    //private Long outlookAugReturnTotal;
    // 8月(実績見通し)_リベート(在庫)
    private Long outlookAugRebateInven;
    // 8月(実績見通し)_リベート(直送)
    private Long outlookAugRebateChokusou;
    // 8月(実績見通し)_リベート計
    //private Long outlookAugRebateTotal;
    // 8月(実績見通し)_センターフィ(在庫)
    private Long outlookAugCenterFeeInven;
    // 8月(実績見通し)_センターフィ(直送)
    private Long outlookAugCenterFeeChokusou;
    // 8月(実績見通し)_センターフィ計
    //private Long outlookAugCenterFeeTotal;
    // 8月(実績見通し)_直接利益(在庫)
    private Long outlookAugChokuRiekiInven;
    // 8月(実績見通し)_直接利益(直送)
    private Long outlookAugChokuRiekiChokusou;
    // 8月(実績見通し)_直接利益計
    //private Long outlookAugChokuRiekiTotal;
    // 8月(実績見通し)_直利率(在庫)
    //private Long outlookAugChokuRateInven;
    // 8月(実績見通し)_直利率(直送)
    //private Long outlookAugChokuRateChokusou;
    // 8月(実績見通し)_直利率計
    //private Long outlookAugChokuRateTotal;

    // 9月(実績見通し)_総売上高(在庫)
    private Long outlookSepAllSalInven;
    // 9月(実績見通し)_総売上高(直送)
    private Long outlookSepAllSalChokusou;
    // 9月(実績見通し)_総売上高計
    //private Long outlookSepAllSalTotal;
    // 9月(実績見通し)_返品(在庫)
    private Long outlookSepReturnInven;
    // 9月(実績見通し)_返品(直送)
    private Long outlookSepReturnChokusou;
    // 9月(実績見通し)_返品計
    //private Long outlookSepReturnTotal;
    // 9月(実績見通し)_リベート(在庫)
    private Long outlookSepRebateInven;
    // 9月(実績見通し)_リベート(直送)
    private Long outlookSepRebateChokusou;
    // 9月(実績見通し)_リベート計
    //private Long outlookSepRebateTotal;
    // 9月(実績見通し)_センターフィ(在庫)
    private Long outlookSepCenterFeeInven;
    // 9月(実績見通し)_センターフィ(直送)
    private Long outlookSepCenterFeeChokusou;
    // 9月(実績見通し)_センターフィ計
    //private Long outlookSepCenterFeeTotal;
    // 9月(実績見通し)_直接利益(在庫)
    private Long outlookSepChokuRiekiInven;
    // 9月(実績見通し)_直接利益(直送)
    private Long outlookSepChokuRiekiChokusou;
    // 9月(実績見通し)_直接利益計
    //private Long outlookSepChokuRiekiTotal;
    // 9月(実績見通し)_直利率(在庫)
    //private Long outlookSepChokuRateInven;
    // 9月(実績見通し)_直利率(直送)
    //private Long outlookSepChokuRateChokusou;
    // 9月(実績見通し)_直利率計
    //private Long outlookSepChokuRateTotal;

    // 10月(実績見通し)_総売上高(在庫)
    private Long outlookOctAllSalInven;
    // 10月(実績見通し)_総売上高(直送)
    private Long outlookOctAllSalChokusou;
    // 10月(実績見通し)_総売上高計
    //private Long outlookOctAllSalTotal;
    // 10月(実績見通し)_返品(在庫)
    private Long outlookOctReturnInven;
    // 10月(実績見通し)_返品(直送)
    private Long outlookOctReturnChokusou;
    // 10月(実績見通し)_返品計
    //private Long outlookOctReturnTotal;
    // 10月(実績見通し)_リベート(在庫)
    private Long outlookOctRebateInven;
    // 10月(実績見通し)_リベート(直送)
    private Long outlookOctRebateChokusou;
    // 10月(実績見通し)_リベート計
    //private Long outlookOctRebateTotal;
    // 10月(実績見通し)_センターフィ(在庫)
    private Long outlookOctCenterFeeInven;
    // 10月(実績見通し)_センターフィ(直送)
    private Long outlookOctCenterFeeChokusou;
    // 10月(実績見通し)_センターフィ計
    //private Long outlookOctCenterFeeTotal;
    // 10月(実績見通し)_直接利益(在庫)
    private Long outlookOctChokuRiekiInven;
    // 10月(実績見通し)_直接利益(直送)
    private Long outlookOctChokuRiekiChokusou;
    // 10月(実績見通し)_直接利益計
    //private Long outlookOctChokuRiekiTotal;
    // 10月(実績見通し)_直利率(在庫)
    //private Long outlookOctChokuRateInven;
    // 10月(実績見通し)_直利率(直送)
    //private Long outlookOctChokuRateChokusou;
    // 10月(実績見通し)_直利率計
    //private Long outlookOctChokuRateTotal;

    // 11月(実績見通し)_総売上高(在庫)
    private Long outlookNovAllSalInven;
    // 11月(実績見通し)_総売上高(直送)
    private Long outlookNovAllSalChokusou;
    // 11月(実績見通し)_総売上高計
    //private Long outlookNovAllSalTotal;
    // 11月(実績見通し)_返品(在庫)
    private Long outlookNovReturnInven;
    // 11月(実績見通し)_返品(直送)
    private Long outlookNovReturnChokusou;
    // 11月(実績見通し)_返品計
    //private Long outlookNovReturnTotal;
    // 11月(実績見通し)_リベート(在庫)
    private Long outlookNovRebateInven;
    // 11月(実績見通し)_リベート(直送)
    private Long outlookNovRebateChokusou;
    // 11月(実績見通し)_リベート計
    //private Long outlookNovRebateTotal;
    // 11月(実績見通し)_センターフィ(在庫)
    private Long outlookNovCenterFeeInven;
    // 11月(実績見通し)_センターフィ(直送)
    private Long outlookNovCenterFeeChokusou;
    // 11月(実績見通し)_センターフィ計
    //private Long outlookNovCenterFeeTotal;
    // 11月(実績見通し)_直接利益(在庫)
    private Long outlookNovChokuRiekiInven;
    // 11月(実績見通し)_直接利益(直送)
    private Long outlookNovChokuRiekiChokusou;
    // 11月(実績見通し)_直接利益計
    //private Long outlookNovChokuRiekiTotal;
    // 11月(実績見通し)_直利率(在庫)
    //private Long outlookNovChokuRateInven;
    // 11月(実績見通し)_直利率(直送)
    //private Long outlookNovChokuRateChokusou;
    // 11月(実績見通し)_直利率計
    //private Long outlookNovChokuRateTotal;

    // 12月(実績見通し)_総売上高(在庫)
    private Long outlookDecAllSalInven;
    // 12月(実績見通し)_総売上高(直送)
    private Long outlookDecAllSalChokusou;
    // 12月(実績見通し)_総売上高計
    //private Long outlookDecAllSalTotal;
    // 12月(実績見通し)_返品(在庫)
    private Long outlookDecReturnInven;
    // 12月(実績見通し)_返品(直送)
    private Long outlookDecReturnChokusou;
    // 12月(実績見通し)_返品計
    //private Long outlookDecReturnTotal;
    // 12月(実績見通し)_リベート(在庫)
    private Long outlookDecRebateInven;
    // 12月(実績見通し)_リベート(直送)
    private Long outlookDecRebateChokusou;
    // 12月(実績見通し)_リベート計
    //private Long outlookDecRebateTotal;
    // 12月(実績見通し)_センターフィ(在庫)
    private Long outlookDecCenterFeeInven;
    // 12月(実績見通し)_センターフィ(直送)
    private Long outlookDecCenterFeeChokusou;
    // 12月(実績見通し)_センターフィ計
    //private Long outlookDecCenterFeeTotal;
    // 12月(実績見通し)_直接利益(在庫)
    private Long outlookDecChokuRiekiInven;
    // 12月(実績見通し)_直接利益(直送)
    private Long outlookDecChokuRiekiChokusou;
    // 12月(実績見通し)_直接利益計
    //private Long outlookDecChokuRiekiTotal;
    // 12月(実績見通し)_直利率(在庫)
    //private Long outlookDecChokuRateInven;
    // 12月(実績見通し)_直利率(直送)
    //private Long outlookDecChokuRateChokusou;
    // 12月(実績見通し)_直利率計
    //private Long outlookDecChokuRateTotal;


    // 1月(実績見通し)_総売上高(在庫)
    @Required(fieldName = "1月(実績見通し)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_総売上高(在庫)")
    private Long outlookJanAllSalInven;
    // 1月(実績見通し)_総売上高(直送)
    @Required(fieldName = "1月(実績見通し)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_総売上高(直送)")
    private Long outlookJanAllSalChokusou;
    // 1月(実績見通し)_総売上高計
    //@Required(fieldName = "1月(実績見通し)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_総売上高計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_総売上高計")
    //private Long outlookJanAllSalTotal;
    // 1月(実績見通し)_返品(在庫)
    @Required(fieldName = "1月(実績見通し)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_返品(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_返品(在庫)")
    private Long outlookJanReturnInven;
    // 1月(実績見通し)_返品(直送)
    @Required(fieldName = "1月(実績見通し)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_返品(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_返品(直送)")
    private Long outlookJanReturnChokusou;
    // 1月(実績見通し)_返品計
    //@Required(fieldName = "1月(実績見通し)_返品計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_返品計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_返品計")
    //private Long outlookJanReturnTotal;
    // 1月(実績見通し)_リベート(在庫)
    @Required(fieldName = "1月(実績見通し)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_リベート(在庫)")
    private Long outlookJanRebateInven;
    // 1月(実績見通し)_リベート(直送)
    @Required(fieldName = "1月(実績見通し)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_リベート(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_リベート(直送)")
    private Long outlookJanRebateChokusou;
    // 1月(実績見通し)_リベート計
    //@Required(fieldName = "1月(実績見通し)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_リベート計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_リベート計")
    //private Long outlookJanRebateTotal;
    // 1月(実績見通し)_センターフィ(在庫)
    @Required(fieldName = "1月(実績見通し)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_センターフィ(在庫)")
    private Long outlookJanCenterFeeInven;
    // 1月(実績見通し)_センターフィ(直送)
    @Required(fieldName = "1月(実績見通し)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_センターフィ(直送)")
    private Long outlookJanCenterFeeChokusou;
    // 1月(実績見通し)_センターフィ計
    //@Required(fieldName = "1月(実績見通し)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_センターフィ計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_センターフィ計")
    //private Long outlookJanCenterFeeTotal;
    // 1月(実績見通し)_直接利益(在庫)
    @Required(fieldName = "1月(実績見通し)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_直接利益(在庫)")
    private Long outlookJanChokuRiekiInven;
    // 1月(実績見通し)_直接利益(直送)
    @Required(fieldName = "1月(実績見通し)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_直接利益(直送)")
    private Long outlookJanChokuRiekiChokusou;
    // 1月(実績見通し)_直接利益計
    //@Required(fieldName = "1月(実績見通し)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直接利益計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_直接利益計")
    //private Long outlookJanChokuRiekiTotal;
    // 1月(実績見通し)_直利率(在庫)
    //@Required(fieldName = "1月(実績見通し)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_直利率(在庫)")
    //private Long outlookJanChokuRateInven;
    // 1月(実績見通し)_直利率(直送)
    //@Required(fieldName = "1月(実績見通し)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_直利率(直送)")
    //private Long outlookJanChokuRateChokusou;
    // 1月(実績見通し)_直利率計
    //@Required(fieldName = "1月(実績見通し)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直利率計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_直利率計")
    //private Long outlookJanChokuRateTotal;
    // 2月(実績見通し)_総売上高(在庫)
    @Required(fieldName = "2月(実績見通し)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_総売上高(在庫)")
    private Long outlookFebAllSalInven;
    // 2月(実績見通し)_総売上高(直送)
    @Required(fieldName = "2月(実績見通し)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_総売上高(直送)")
    private Long outlookFebAllSalChokusou;
    // 2月(実績見通し)_総売上高計
    //@Required(fieldName = "2月(実績見通し)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_総売上高計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_総売上高計")
    //private Long outlookFebAllSalTotal;
    // 2月(実績見通し)_返品(在庫)
    @Required(fieldName = "2月(実績見通し)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_返品(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_返品(在庫)")
    private Long outlookFebReturnInven;
    // 2月(実績見通し)_返品(直送)
    @Required(fieldName = "2月(実績見通し)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_返品(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_返品(直送)")
    private Long outlookFebReturnChokusou;
    // 2月(実績見通し)_返品計
    //@Required(fieldName = "2月(実績見通し)_返品計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_返品計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_返品計")
    //private Long outlookFebReturnTotal;
    // 2月(実績見通し)_リベート(在庫)
    @Required(fieldName = "2月(実績見通し)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_リベート(在庫)")
    private Long outlookFebRebateInven;
    // 2月(実績見通し)_リベート(直送)
    @Required(fieldName = "2月(実績見通し)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_リベート(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_リベート(直送)")
    private Long outlookFebRebateChokusou;
    // 2月(実績見通し)_リベート計
    //@Required(fieldName = "2月(実績見通し)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_リベート計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_リベート計")
    //private Long outlookFebRebateTotal;
    // 2月(実績見通し)_センターフィ(在庫)
    @Required(fieldName = "2月(実績見通し)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_センターフィ(在庫)")
    private Long outlookFebCenterFeeInven;
    // 2月(実績見通し)_センターフィ(直送)
    @Required(fieldName = "2月(実績見通し)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_センターフィ(直送)")
    private Long outlookFebCenterFeeChokusou;
    // 2月(実績見通し)_センターフィ計
    //@Required(fieldName = "2月(実績見通し)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_センターフィ計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_センターフィ計")
    //private Long outlookFebCenterFeeTotal;
    // 2月(実績見通し)_直接利益(在庫)
    @Required(fieldName = "2月(実績見通し)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_直接利益(在庫)")
    private Long outlookFebChokuRiekiInven;
    // 2月(実績見通し)_直接利益(直送)
    @Required(fieldName = "2月(実績見通し)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_直接利益(直送)")
    private Long outlookFebChokuRiekiChokusou;
    // 2月(実績見通し)_直接利益計
    //@Required(fieldName = "2月(実績見通し)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直接利益計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_直接利益計")
    //private Long outlookFebChokuRiekiTotal;
    // 2月(実績見通し)_直利率(在庫)
    //@Required(fieldName = "2月(実績見通し)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_直利率(在庫)")
    //private Long outlookFebChokuRateInven;
    // 2月(実績見通し)_直利率(直送)
    //@Required(fieldName = "2月(実績見通し)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_直利率(直送)")
    //private Long outlookFebChokuRateChokusou;
    // 2月(実績見通し)_直利率計
    //@Required(fieldName = "2月(実績見通し)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直利率計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_直利率計")
    //private Long outlookFebChokuRateTotal;
    // 3月(実績見通し)_総売上高(在庫)
    @Required(fieldName = "3月(実績見通し)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_総売上高(在庫)")
    private Long outlookMarAllSalInven;
    // 3月(実績見通し)_総売上高(直送)
    @Required(fieldName = "3月(実績見通し)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_総売上高(直送)")
    private Long outlookMarAllSalChokusou;
    // 3月(実績見通し)_総売上高計
    //@Required(fieldName = "3月(実績見通し)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_総売上高計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_総売上高計")
    //private Long outlookMarAllSalTotal;
    // 3月(実績見通し)_返品(在庫)
    @Required(fieldName = "3月(実績見通し)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_返品(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_返品(在庫)")
    private Long outlookMarReturnInven;
    // 3月(実績見通し)_返品(直送)
    @Required(fieldName = "3月(実績見通し)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_返品(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_返品(直送)")
    private Long outlookMarReturnChokusou;
    // 3月(実績見通し)_返品計
    //@Required(fieldName = "3月(実績見通し)_返品計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_返品計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_返品計")
    //private Long outlookMarReturnTotal;
    // 3月(実績見通し)_リベート(在庫)
    @Required(fieldName = "3月(実績見通し)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_リベート(在庫)")
    private Long outlookMarRebateInven;
    // 3月(実績見通し)_リベート(直送)
    @Required(fieldName = "3月(実績見通し)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_リベート(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_リベート(直送)")
    private Long outlookMarRebateChokusou;
    // 3月(実績見通し)_リベート計
    //@Required(fieldName = "3月(実績見通し)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_リベート計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_リベート計")
    //private Long outlookMarRebateTotal;
    // 3月(実績見通し)_センターフィ(在庫)
    @Required(fieldName = "3月(実績見通し)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_センターフィ(在庫)")
    private Long outlookMarCenterFeeInven;
    // 3月(実績見通し)_センターフィ(直送)
    @Required(fieldName = "3月(実績見通し)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_センターフィ(直送)")
    private Long outlookMarCenterFeeChokusou;
    // 3月(実績見通し)_センターフィ計
    //@Required(fieldName = "3月(実績見通し)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_センターフィ計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_センターフィ計")
    //private Long outlookMarCenterFeeTotal;
    // 3月(実績見通し)_直接利益(在庫)
    @Required(fieldName = "3月(実績見通し)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_直接利益(在庫)")
    private Long outlookMarChokuRiekiInven;
    // 3月(実績見通し)_直接利益(直送)
    @Required(fieldName = "3月(実績見通し)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_直接利益(直送)")
    private Long outlookMarChokuRiekiChokusou;
    // 3月(実績見通し)_直接利益計
    //@Required(fieldName = "3月(実績見通し)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直接利益計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_直接利益計")
    //private Long outlookMarChokuRiekiTotal;
    // 3月(実績見通し)_直利率(在庫)
    //@Required(fieldName = "3月(実績見通し)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_直利率(在庫)")
    //private Long outlookMarChokuRateInven;
    // 3月(実績見通し)_直利率(直送)
    //@Required(fieldName = "3月(実績見通し)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_直利率(直送)")
    //private Long outlookMarChokuRateChokusou;
    // 3月(実績見通し)_直利率計
    //@Required(fieldName = "3月(実績見通し)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直利率計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_直利率計")
    //private Long outlookMarChokuRateTotal;

    // ファイル情報1
    private String fileInfo1;
    private String fileInfo2;

    /**
     * データタイプ   1:次年度計画マスタ
     *             2:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜本社＞
     *             3:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜エリア＞
     *             4:間接利益計画_メーカー別
     */
    private String dataType;

    /**
     * エリアリスト   画面選択されたエリアのエリアコードリスト
     *              採算管理単位計画策定エリアが選択された場合は"SKSA"
     *              複数のエリアを指定可能
     */
    private List<AreaInfo> areaList;

    /**
     * 本社場所区分  0:本社
     *             1:場所
     */
    private String hnshBashoKubun;

    /**
     * データ区分リスト  0:移管前（当年度組織）
     *                1:移管後（次年度組織）
     *                複数のデータ区分を指定可能
     */
    private List<String> dataKubun;

    // ==================== 便利メソッド ====================
    /**
     * データ区分リストをカンマ区切り文字列として取得
     * @return カンマ区切りのデータ区分文字列
     */
    public String getDataKubunString() {
        if (dataKubun == null || dataKubun.isEmpty()) {
            return null;
        }
        return String.join(",", dataKubun);
    }

    @Override
    public Map<String, Object> toDatabaseFieldsCore(boolean isInsert) {
        Map<String, Object> fields = new HashMap<>();

        if (isInsert) {
            // 挿入時のみ設定するフィールド
            // 年度（次年度を自動設定）
            fields.put("NENDO", DateUtil.getNextFiscalYear());
            fields.put("GROUP_CODE", groupCode);
            fields.put("SSNKN_TNCD", saisanCode);
        }

        // 基本フィールド
        fields.put("AREA_CODE", areaCode);
        fields.put("UNIT_CODE", unitCode);
        fields.put("TNTSH_MEI_KANJI", tantoshaName);
        fields.put("KIGYO_CODE", kigyoCode);
        fields.put("SUB_CTGRY_MEI_TNSHK_KANA", kategori);
        fields.put("SUB_CTGRY_MEI_TNSHK_KANJI", subKategori);
        fields.put("GYT_MEI", gyotai);
        fields.put("TRKM_KUBUN", afterTorikumKbn);
        fields.put("GYT_HRTS", gyotaiHiritsu);

        // 各種金額
        // 計画＿在庫売上＿１月目
        fields.put("KKK_ZAIKO_URG_1_TSKM", planAprAllSalInven * 1000);
        // 計画＿直送売上＿１月目
        fields.put("KKK_CHKS_URG_1_TSKM", planAprAllSalChokusou * 1000);
        // 計画＿在庫返品等＿１月目
        fields.put("KKK_ZAIKO_HMPNT_1_TSKM", planAprReturnInven * 1000);
        // 計画＿直送返品等＿１月目
        fields.put("KKK_CHKS_HMPNT_1_TSKM", planAprReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿１月目
        fields.put("KKK_ZAIKO_SHHR_RBT_1_TSKM", planAprRebateInven * 1000);
        // 計画＿直送支払リベート＿１月目
        fields.put("KKK_CHKS_SHHR_RBT_1_TSKM", planAprRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿１月目
        fields.put("KKK_ZAIKO_CNTR_FEE_1_TSKM", planAprCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿１月目
        fields.put("KKK_CHKS_CNTR_FEE_1_TSKM", planAprCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿１月目
        fields.put("KKK_ZAIKO_RIEKI_1_TSKM", planAprChokuRiekiInven * 1000);
        // 計画＿直送利益＿１月目
        fields.put("KKK_CHKS_RIEKI_1_TSKM", planAprChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿２月目
        fields.put("KKK_ZAIKO_URG_2_TSKM", planMayAllSalInven * 1000);
        // 計画＿直送売上＿２月目
        fields.put("KKK_CHKS_URG_2_TSKM", planMayAllSalChokusou * 1000);
        // 計画＿在庫返品等＿２月目
        fields.put("KKK_ZAIKO_HMPNT_2_TSKM", planMayReturnInven * 1000);
        // 計画＿直送返品等＿２月目
        fields.put("KKK_CHKS_HMPNT_2_TSKM", planMayReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿２月目
        fields.put("KKK_ZAIKO_SHHR_RBT_2_TSKM", planMayRebateInven * 1000);
        // 計画＿直送支払リベート＿２月目
        fields.put("KKK_CHKS_SHHR_RBT_2_TSKM", planMayRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿２月目
        fields.put("KKK_ZAIKO_CNTR_FEE_2_TSKM", planMayCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿２月目
        fields.put("KKK_CHKS_CNTR_FEE_2_TSKM", planMayCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿２月目
        fields.put("KKK_ZAIKO_RIEKI_2_TSKM", planMayChokuRiekiInven * 1000);
        // 計画＿直送利益＿２月目
        fields.put("KKK_CHKS_RIEKI_2_TSKM", planMayChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿３月目
        fields.put("KKK_ZAIKO_URG_3_TSKM", planJunAllSalInven * 1000);
        // 計画＿直送売上＿３月目
        fields.put("KKK_CHKS_URG_3_TSKM", planJunAllSalChokusou * 1000);
        // 計画＿在庫返品等＿３月目
        fields.put("KKK_ZAIKO_HMPNT_3_TSKM", planJunReturnInven * 1000);
        // 計画＿直送返品等＿３月目
        fields.put("KKK_CHKS_HMPNT_3_TSKM", planJunReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿３月目
        fields.put("KKK_ZAIKO_SHHR_RBT_3_TSKM", planJunRebateInven * 1000);
        // 計画＿直送支払リベート＿３月目
        fields.put("KKK_CHKS_SHHR_RBT_3_TSKM", planJunRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿３月目
        fields.put("KKK_ZAIKO_CNTR_FEE_3_TSKM", planJunCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿３月目
        fields.put("KKK_CHKS_CNTR_FEE_3_TSKM", planJunCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿３月目
        fields.put("KKK_ZAIKO_RIEKI_3_TSKM", planJunChokuRiekiInven * 1000);
        // 計画＿直送利益＿３月目
        fields.put("KKK_CHKS_RIEKI_3_TSKM", planJunChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿４月目
        fields.put("KKK_ZAIKO_URG_4_TSKM", planJulAllSalInven * 1000);
        // 計画＿直送売上＿４月目
        fields.put("KKK_CHKS_URG_4_TSKM", planJulAllSalChokusou * 1000);
        // 計画＿在庫返品等＿４月目
        fields.put("KKK_ZAIKO_HMPNT_4_TSKM", planJulReturnInven * 1000);
        // 計画＿直送返品等＿４月目
        fields.put("KKK_CHKS_HMPNT_4_TSKM", planJulReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿４月目
        fields.put("KKK_ZAIKO_SHHR_RBT_4_TSKM", planJulRebateInven * 1000);
        // 計画＿直送支払リベート＿４月目
        fields.put("KKK_CHKS_SHHR_RBT_4_TSKM", planJulRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿４月目
        fields.put("KKK_ZAIKO_CNTR_FEE_4_TSKM", planJulCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿４月目
        fields.put("KKK_CHKS_CNTR_FEE_4_TSKM", planJulCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿４月目
        fields.put("KKK_ZAIKO_RIEKI_4_TSKM", planJulChokuRiekiInven * 1000);
        // 計画＿直送利益＿４月目
        fields.put("KKK_CHKS_RIEKI_4_TSKM", planJulChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿５月目
        fields.put("KKK_ZAIKO_URG_5_TSKM", planAugAllSalInven * 1000);
        // 計画＿直送売上＿５月目
        fields.put("KKK_CHKS_URG_5_TSKM", planAugAllSalChokusou * 1000);
        // 計画＿在庫返品等＿５月目
        fields.put("KKK_ZAIKO_HMPNT_5_TSKM", planAugReturnInven * 1000);
        // 計画＿直送返品等＿５月目
        fields.put("KKK_CHKS_HMPNT_5_TSKM", planAugReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿５月目
        fields.put("KKK_ZAIKO_SHHR_RBT_5_TSKM", planAugRebateInven * 1000);
        // 計画＿直送支払リベート＿５月目
        fields.put("KKK_CHKS_SHHR_RBT_5_TSKM", planAugRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿５月目
        fields.put("KKK_ZAIKO_CNTR_FEE_5_TSKM", planAugCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿５月目
        fields.put("KKK_CHKS_CNTR_FEE_5_TSKM", planAugCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿５月目
        fields.put("KKK_ZAIKO_RIEKI_5_TSKM", planAugChokuRiekiInven * 1000);
        // 計画＿直送利益＿５月目
        fields.put("KKK_CHKS_RIEKI_5_TSKM", planAugChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿６月目
        fields.put("KKK_ZAIKO_URG_6_TSKM", planSepAllSalInven * 1000);
        // 計画＿直送売上＿６月目
        fields.put("KKK_CHKS_URG_6_TSKM", planSepAllSalChokusou * 1000);
        // 計画＿在庫返品等＿６月目
        fields.put("KKK_ZAIKO_HMPNT_6_TSKM", planSepReturnInven * 1000);
        // 計画＿直送返品等＿６月目
        fields.put("KKK_CHKS_HMPNT_6_TSKM", planSepReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿６月目
        fields.put("KKK_ZAIKO_SHHR_RBT_6_TSKM", planSepRebateInven * 1000);
        // 計画＿直送支払リベート＿６月目
        fields.put("KKK_CHKS_SHHR_RBT_6_TSKM", planSepRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿６月目
        fields.put("KKK_ZAIKO_CNTR_FEE_6_TSKM", planSepCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿６月目
        fields.put("KKK_CHKS_CNTR_FEE_6_TSKM", planSepCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿６月目
        fields.put("KKK_ZAIKO_RIEKI_6_TSKM", planSepChokuRiekiInven * 1000);
        // 計画＿直送利益＿６月目
        fields.put("KKK_CHKS_RIEKI_6_TSKM", planSepChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿７月目
        fields.put("KKK_ZAIKO_URG_7_TSKM", planOctAllSalInven * 1000);
        // 計画＿直送売上＿７月目
        fields.put("KKK_CHKS_URG_7_TSKM", planOctAllSalChokusou * 1000);
        // 計画＿在庫返品等＿７月目
        fields.put("KKK_ZAIKO_HMPNT_7_TSKM", planOctReturnInven * 1000);
        // 計画＿直送返品等＿７月目
        fields.put("KKK_CHKS_HMPNT_7_TSKM", planOctReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿７月目
        fields.put("KKK_ZAIKO_SHHR_RBT_7_TSKM", planOctRebateInven * 1000);
        // 計画＿直送支払リベート＿７月目
        fields.put("KKK_CHKS_SHHR_RBT_7_TSKM", planOctRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿７月目
        fields.put("KKK_ZAIKO_CNTR_FEE_7_TSKM", planOctCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿７月目
        fields.put("KKK_CHKS_CNTR_FEE_7_TSKM", planOctCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿７月目
        fields.put("KKK_ZAIKO_RIEKI_7_TSKM", planOctChokuRiekiInven * 1000);
        // 計画＿直送利益＿７月目
        fields.put("KKK_CHKS_RIEKI_7_TSKM", planOctChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿８月目
        fields.put("KKK_ZAIKO_URG_8_TSKM", planNovAllSalInven * 1000);
        // 計画＿直送売上＿８月目
        fields.put("KKK_CHKS_URG_8_TSKM", planNovAllSalChokusou * 1000);
        // 計画＿在庫返品等＿８月目
        fields.put("KKK_ZAIKO_HMPNT_8_TSKM", planNovReturnInven * 1000);
        // 計画＿直送返品等＿８月目
        fields.put("KKK_CHKS_HMPNT_8_TSKM", planNovReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿８月目
        fields.put("KKK_ZAIKO_SHHR_RBT_8_TSKM", planNovRebateInven * 1000);
        // 計画＿直送支払リベート＿８月目
        fields.put("KKK_CHKS_SHHR_RBT_8_TSKM", planNovRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿８月目
        fields.put("KKK_ZAIKO_CNTR_FEE_8_TSKM", planNovCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿８月目
        fields.put("KKK_CHKS_CNTR_FEE_8_TSKM", planNovCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿８月目
        fields.put("KKK_ZAIKO_RIEKI_8_TSKM", planNovChokuRiekiInven * 1000);
        // 計画＿直送利益＿８月目
        fields.put("KKK_CHKS_RIEKI_8_TSKM", planNovChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿９月目
        fields.put("KKK_ZAIKO_URG_9_TSKM", planDecAllSalInven * 1000);
        // 計画＿直送売上＿９月目
        fields.put("KKK_CHKS_URG_9_TSKM", planDecAllSalChokusou * 1000);
        // 計画＿在庫返品等＿９月目
        fields.put("KKK_ZAIKO_HMPNT_9_TSKM", planDecReturnInven * 1000);
        // 計画＿直送返品等＿９月目
        fields.put("KKK_CHKS_HMPNT_9_TSKM", planDecReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿９月目
        fields.put("KKK_ZAIKO_SHHR_RBT_9_TSKM", planDecRebateInven * 1000);
        // 計画＿直送支払リベート＿９月目
        fields.put("KKK_CHKS_SHHR_RBT_9_TSKM", planDecRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿９月目
        fields.put("KKK_ZAIKO_CNTR_FEE_9_TSKM", planDecCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿９月目
        fields.put("KKK_CHKS_CNTR_FEE_9_TSKM", planDecCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿９月目
        fields.put("KKK_ZAIKO_RIEKI_9_TSKM", planDecChokuRiekiInven * 1000);
        // 計画＿直送利益＿９月目
        fields.put("KKK_CHKS_RIEKI_9_TSKM", planDecChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿１０月目
        fields.put("KKK_ZAIKO_URG_10_TSKM", planJanAllSalInven * 1000);
        // 計画＿直送売上＿１０月目
        fields.put("KKK_CHKS_URG_10_TSKM", planJanAllSalChokusou * 1000);
        // 計画＿在庫返品等＿１０月目
        fields.put("KKK_ZAIKO_HMPNT_10_TSKM", planJanReturnInven * 1000);
        // 計画＿直送返品等＿１０月目
        fields.put("KKK_CHKS_HMPNT_10_TSKM", planJanReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿１０月目
        fields.put("KKK_ZAIKO_SHHR_RBT_10_TSKM", planJanRebateInven * 1000);
        // 計画＿直送支払リベート＿１０月目
        fields.put("KKK_CHKS_SHHR_RBT_10_TSKM", planJanRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿１０月目
        fields.put("KKK_ZAIKO_CNTR_FEE_10_TSKM", planJanCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿１０月目
        fields.put("KKK_CHKS_CNTR_FEE_10_TSKM", planJanCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿１０月目
        fields.put("KKK_ZAIKO_RIEKI_10_TSKM", planJanChokuRiekiInven * 1000);
        // 計画＿直送利益＿１０月目
        fields.put("KKK_CHKS_RIEKI_10_TSKM", planJanChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿１１月目
        fields.put("KKK_ZAIKO_URG_11_TSKM", planFebAllSalInven * 1000);
        // 計画＿直送売上＿１１月目
        fields.put("KKK_CHKS_URG_11_TSKM", planFebAllSalChokusou * 1000);
        // 計画＿在庫返品等＿１１月目
        fields.put("KKK_ZAIKO_HMPNT_11_TSKM", planFebReturnInven * 1000);
        // 計画＿直送返品等＿１１月目
        fields.put("KKK_CHKS_HMPNT_11_TSKM", planFebReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿１１月目
        fields.put("KKK_ZAIKO_SHHR_RBT_11_TSKM", planFebRebateInven * 1000);
        // 計画＿直送支払リベート＿１１月目
        fields.put("KKK_CHKS_SHHR_RBT_11_TSKM", planFebRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿１１月目
        fields.put("KKK_ZAIKO_CNTR_FEE_11_TSKM", planFebCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿１１月目
        fields.put("KKK_CHKS_CNTR_FEE_11_TSKM", planFebCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿１１月目
        fields.put("KKK_ZAIKO_RIEKI_11_TSKM", planFebChokuRiekiInven * 1000);
        // 計画＿直送利益＿１１月目
        fields.put("KKK_CHKS_RIEKI_11_TSKM", planFebChokuRiekiChokusou * 1000);
        // 計画＿在庫売上＿１２月目
        fields.put("KKK_ZAIKO_URG_12_TSKM", planMarAllSalInven * 1000);
        // 計画＿直送売上＿１２月目
        fields.put("KKK_CHKS_URG_12_TSKM", planMarAllSalChokusou * 1000);
        // 計画＿在庫返品等＿１２月目
        fields.put("KKK_ZAIKO_HMPNT_12_TSKM", planMarReturnInven * 1000);
        // 計画＿直送返品等＿１２月目
        fields.put("KKK_CHKS_HMPNT_12_TSKM", planMarReturnChokusou * 1000);
        // 計画＿在庫支払リベート＿１２月目
        fields.put("KKK_ZAIKO_SHHR_RBT_12_TSKM", planMarRebateInven * 1000);
        // 計画＿直送支払リベート＿１２月目
        fields.put("KKK_CHKS_SHHR_RBT_12_TSKM", planMarRebateChokusou * 1000);
        // 計画＿在庫センターフィ＿１２月目
        fields.put("KKK_ZAIKO_CNTR_FEE_12_TSKM", planMarCenterFeeInven * 1000);
        // 計画＿直送センターフィ＿１２月目
        fields.put("KKK_CHKS_CNTR_FEE_12_TSKM", planMarCenterFeeChokusou * 1000);
        // 計画＿在庫利益＿１２月目
        fields.put("KKK_ZAIKO_RIEKI_12_TSKM", planMarChokuRiekiInven * 1000);
        // 計画＿直送利益＿１２月目
        fields.put("KKK_CHKS_RIEKI_12_TSKM", planMarChokuRiekiChokusou * 1000);
        // 実績見通し＿在庫売上＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_URG_10_TSKM", outlookJanAllSalInven * 1000);
        // 実績見通し＿直送売上＿１０月目
        fields.put("JSSK_MTSH_CHKS_URG_10_TSKM", outlookJanAllSalChokusou * 1000);
        // 実績見通し＿在庫返品等＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_10_TSKM", outlookJanReturnInven * 1000);
        // 実績見通し＿直送返品等＿１０月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_10_TSKM", outlookJanReturnChokusou * 1000);
        // 実績見通し＿在庫支払リベート＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_10_TSKM", outlookJanRebateInven * 1000);
        // 実績見通し＿直送支払リベート＿１０月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_10_TSKM", outlookJanRebateChokusou * 1000);
        // 実績見通し＿在庫センターフィ＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_10_TSKM", outlookJanCenterFeeInven * 1000);
        // 実績見通し＿直送センターフィ＿１０月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_10_TSKM", outlookJanCenterFeeChokusou * 1000);
        // 実績見通し＿在庫利益＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_10_TSKM", outlookJanChokuRiekiInven * 1000);
        // 実績見通し＿直送利益＿１０月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_10_TSKM", outlookJanChokuRiekiChokusou * 1000);
        // 実績見通し＿在庫売上＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_URG_11_TSKM", outlookFebAllSalInven * 1000);
        // 実績見通し＿直送売上＿１１月目
        fields.put("JSSK_MTSH_CHKS_URG_11_TSKM", outlookFebAllSalChokusou * 1000);
        // 実績見通し＿在庫返品等＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_11_TSKM", outlookFebReturnInven * 1000);
        // 実績見通し＿直送返品等＿１１月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_11_TSKM", outlookFebReturnChokusou * 1000);
        // 実績見通し＿在庫支払リベート＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_11_TSKM", outlookFebRebateInven * 1000);
        // 実績見通し＿直送支払リベート＿１１月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_11_TSKM", outlookFebRebateChokusou * 1000);
        // 実績見通し＿在庫センターフィ＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_11_TSKM", outlookFebCenterFeeInven * 1000);
        // 実績見通し＿直送センターフィ＿１１月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_11_TSKM", outlookFebCenterFeeChokusou * 1000);
        // 実績見通し＿在庫利益＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_11_TSKM", outlookFebChokuRiekiInven * 1000);
        // 実績見通し＿直送利益＿１１月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_11_TSKM", outlookFebChokuRiekiChokusou * 1000);
        // 実績見通し＿在庫売上＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_URG_12_TSKM", outlookMarAllSalInven * 1000);
        // 実績見通し＿直送売上＿１２月目
        fields.put("JSSK_MTSH_CHKS_URG_12_TSKM", outlookMarAllSalChokusou * 1000);
        // 実績見通し＿在庫返品等＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_12_TSKM", outlookMarReturnInven * 1000);
        // 実績見通し＿直送返品等＿１２月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_12_TSKM", outlookMarReturnChokusou * 1000);
        // 実績見通し＿在庫支払リベート＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_12_TSKM", outlookMarRebateInven * 1000);
        // 実績見通し＿直送支払リベート＿１２月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_12_TSKM", outlookMarRebateChokusou * 1000);
        // 実績見通し＿在庫センターフィ＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_12_TSKM", outlookMarCenterFeeInven * 1000);
        // 実績見通し＿直送センターフィ＿１２月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_12_TSKM", outlookMarCenterFeeChokusou * 1000);
        // 実績見通し＿在庫利益＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_12_TSKM", outlookMarChokuRiekiInven * 1000);
        // 実績見通し＿直送利益＿１２月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_12_TSKM", outlookMarChokuRiekiChokusou * 1000);

        // パラメータ.ファイル種別 = 3:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜エリア＞　の場合
        // 計画＿総売上高計登録
        ImportRequest importRequest = RequestContext.getImportRequest();
        dataType = importRequest.getDataType();
        if(dataType.equals(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE))
        {
            // 計画＿総売上高計
            // 下記項目の合計値でセットする
            // CSV.【採算管理単位コード単位内の1行目】計画4月総売上高在庫～計画3月総売上高在庫
            // CSV.【採算管理単位コード単位内の2行目】計画4月総売上高直送～計画3月総売上高直送
            Long sumTotal = planAprAllSalInven + planMayAllSalInven + planJunAllSalInven + planJulAllSalInven +
                            planAugAllSalInven + planSepAllSalInven + planOctAllSalInven + planNovAllSalInven +
                            planDecAllSalInven + planJanAllSalInven + planFebAllSalInven + planMarAllSalInven +
                            planAprAllSalChokusou + planMayAllSalChokusou + planJunAllSalChokusou + planJulAllSalChokusou +
                            planAugAllSalChokusou + planSepAllSalChokusou + planOctAllSalChokusou + planNovAllSalChokusou +
                            planDecAllSalChokusou + planJanAllSalChokusou + planFebAllSalChokusou + planMarAllSalChokusou;

            fields.put("KKK_URG_KEI", sumTotal * 1000);
        }

        // プログラムIDをFunctionUtilから取得
        String programId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE,
                BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE).getFunctionId();
        if(dataType.equals(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE))
        {
            programId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE,
                    BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE).getFunctionId();
        }
        // 自動生成フィールド
        Date currentTime = new Date();
        String currentTimeStr = DateUtil.formatDateTime(currentTime);

        if (isInsert) {
            // 挿入時のみ設定するフィールド
            fields.put("TRK_PRGRM_ID", programId);
            fields.put("RCRD_TRK_NCHJ", currentTimeStr);
            fields.put("VRSN", 1);
        }

        // 更新時は常に設定
        fields.put("KSHN_PRGRM_ID", programId);
        fields.put("RCRD_KSHN_NCHJ", currentTimeStr);

        return fields;
    }
}
