package com.ms.bp.domain.file.base;

import java.util.List;
import java.util.Map;

/**
 * データ展開インターフェース
 * データベースから取得した1行のデータを複数行に展開する処理を定義
 * 
 * 使用例：
 * - ユーザーの複数ロールを各行に展開
 * - 部門の階層構造を各レベルに展開
 * - 権限の詳細を各権限ごとに展開
 */
public interface DataExpander {
    
    /**
     * 単行データを複数行データに展開
     * 
     * @param rawData データベースから取得した原始単行データ
     * @param context 展開処理のコンテキスト情報
     * @return 展開後の複数行データのリスト（展開不要な場合は元データを含む1要素のリスト）
     */
    List<Map<String, Object>> expandData(Map<String, Object> rawData, ExpansionContext context);
    
    /**
     * データ展開が必要かどうかを判定
     * パフォーマンス最適化のため、展開不要なデータの処理をスキップ
     * 
     * @param rawData 判定対象の原始データ
     * @return true: 展開処理が必要、false: 展開不要（そのまま使用）
     */
    boolean needsExpansion(Map<String, Object> rawData);
    
    /**
     * 展開処理の説明を取得（ログ出力用）
     * 
     * @return 展開処理の説明文字列
     */
    default String getDescription() {
        return this.getClass().getSimpleName();
    }
}
