package com.ms.bp.domain.file.model;

import com.ms.bp.shared.common.io.validation.annotation.*;
import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.FunctionUtil;
import com.ms.bp.shared.common.constants.BusinessConstants;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 次年度計画マスタインポートデータ領域モデル
 * ファイル処理領域における次年度計画マスタデータのインポート処理を担当
 * T_JINENDO_KKK表への登録・更新処理に対応
 * 
 * 主な機能：
 * 1. CSVデータの検証（必須チェック、桁数チェック、書式チェック）
 * 2. 権限チェック（エリアコード、移管先エリアコード）
 * 3. データベースフィールドへの自動マッピング
 * 4. 適用フィールドの自動計算（移管先優先ロジック）
 */
@Data
public class PlanMasterImportData implements DatabaseMappable {
    
    // ==================== CSVフィールド（必須項目） ====================
    
    /**
     * エリアコード
     * 権限チェック対象
     */
    @Required(fieldName = "エリアCD")
    @Range(min = 4, max = 4, fieldName = "エリアCD")
    @NumericHalfWidth(fieldName = "エリアCD")
    private String areaCode;
    
    /**
     * カテゴリコード
     */
    @Required(fieldName = "カテCD")
    @Range(min = 4, max = 4, fieldName = "カテCD")
    @NumericHalfWidth(fieldName = "カテCD")
    private String categoryCode;
    
    /**
     * グループコード
     * 複合主キーの一部
     */
    @Required(fieldName = "グループ")
    @Range(min = 4, max = 4, fieldName = "グループ")
    @NumericHalfWidth(fieldName = "グループ")
    private String groupCode;
    
    /**
     * ユニットコード
     */
    @Required(fieldName = "ユニット")
    @Range(min = 5, max = 5, fieldName = "ユニット")
    @NumericHalfWidth(fieldName = "ユニット")
    private String unitCode;
    
    /**
     * 企業コード
     */
    @Required(fieldName = "企業CD")
    @Range(min = 7, max = 7, fieldName = "企業CD")
    @NumericHalfWidth(fieldName = "企業CD")
    private String kigyoCode;
    
    /**
     * 業態集計
     */
    @Required(fieldName = "業態集計")
    @Range(min = 2, max = 2, fieldName = "業態集計")
    @NumericHalfWidth(fieldName = "業態集計")
    private String gyotaiShukei;
    
    /**
     * 採算管理単位コード
     * 複合主キーの一部
     * マスタにない採算管理単位コードも入力可能
     */
    @Required(fieldName = "採算管理単位CD")
    @Range(min = 7, max = 7, fieldName = "採算管理単位CD")
    private String saisaknKanriTaniCode;
    
    /**
     * 変更後取組区分
     */
    @Required(fieldName = "変更後取組区分")
    @Range(min = 1, max = 20, fieldName = "変更後取組区分")
    private String henkogoTorikumiKubun;
    
    // ==================== CSVフィールド（任意項目） ====================
    
    /**
     * 担当者名
     */
    @Range(min = 1, max = 20, fieldName = "担当者")
    private String tantoshaName;
    
    /**
     * 業態名
     */
    @Range(min = 1, max = 30, fieldName = "業態名")
    private String gyotaiName;
    
    /**
     * サブカテゴリコード
     */
    private String subCategoryCode;
    
    /**
     * 採算管理単位名
     */
    @Range(min = 1, max = 25, fieldName = "採算管理単位名")
    private String saisaknKanriTaniName;
    
    /**
     * 移管先エリアコード
     * 権限チェック対象（値が存在する場合）
     * 組織エリアマスタ存在チェック対象
     */
    @Range(min = 4, max = 4, fieldName = "移管先エリアCD")
    @NumericHalfWidth(fieldName = "移管先エリアCD")
    private String ikansakiAreaCode;
    
    /**
     * 移管先グループコード
     * グループマスタ存在チェック対象
     */
    @Range(min = 4, max = 4, fieldName = "移管先グループCD")
    @NumericHalfWidth(fieldName = "移管先グループCD")
    private String ikansakiGroupCode;
    
    /**
     * 移管先ユニットコード
     * ユニットマスタ存在チェック対象
     */
    @Range(min = 5, max = 5, fieldName = "移管先ユニットCD")
    @NumericHalfWidth(fieldName = "移管先ユニットCD")
    private String ikansakiUnitCode;
    
    // ==================== 参考フィールド（データベースに保存しない） ====================
    
    /**
     * エリア名（参考用）
     */
    private String areaName;
    
    /**
     * カテゴリ名（参考用）
     */
    private String categoryName;
    
    /**
     * 企業名（参考用）
     */
    private String kigyoName;
    
    /**
     * サブカテゴリ名（参考用）
     */
    private String subCategoryName;
    
    /**
     * 変更前取組区分（参考用）
     */
    private String henkomaeTorkumiKubun;
    
    /**
     * 移管先エリア名（参考用）
     */
    private String ikansakiAreaName;
    
    /**
     * 当年実績累計（参考用）
     */
    private String currentYearActual;
    
    /**
     * 当年度計画累計（参考用）
     */
    private String currentYearPlan;
    
    // ==================== 業務ロジックメソッド ====================
    
    /**
     * 移管先エリアコードが設定されているかチェック
     * @return 移管先エリアコードが存在する場合true
     */
    public boolean hasIkansakiAreaCode() {
        return ikansakiAreaCode != null && !ikansakiAreaCode.trim().isEmpty();
    }
    
    /**
     * 移管先グループコードが設定されているかチェック
     * @return 移管先グループコードが存在する場合true
     */
    public boolean hasIkansakiGroupCode() {
        return ikansakiGroupCode != null && !ikansakiGroupCode.trim().isEmpty();
    }
    
    /**
     * 移管先ユニットコードが設定されているかチェック
     * @return 移管先ユニットコードが存在する場合true
     */
    public boolean hasIkansakiUnitCode() {
        return ikansakiUnitCode != null && !ikansakiUnitCode.trim().isEmpty();
    }
    
    /**
     * 適用エリアコードを取得
     * 移管先エリアコードがある場合はそれを、なければエリアコードを返す
     * @return 適用エリアコード
     */
    public String getApplicableAreaCode() {
        return hasIkansakiAreaCode() ? ikansakiAreaCode : areaCode;
    }
    
    /**
     * 適用グループコードを取得
     * 移管先グループコードがある場合はそれを、なければグループコードを返す
     * @return 適用グループコード
     */
    public String getApplicableGroupCode() {
        return hasIkansakiGroupCode() ? ikansakiGroupCode : groupCode;
    }
    
    /**
     * 適用ユニットコードを取得
     * 移管先ユニットコードがある場合はそれを、なければユニットコードを返す
     * @return 適用ユニットコード
     */
    public String getApplicableUnitCode() {
        return hasIkansakiUnitCode() ? ikansakiUnitCode : unitCode;
    }
    
    /**
     * 権限チェックが必要なエリアコードのリストを取得
     * @return 権限チェック対象のエリアコードリスト
     */
    public java.util.List<String> getAreaCodesForPermissionCheck() {
        java.util.List<String> areaCodes = new java.util.ArrayList<>();
        areaCodes.add(areaCode);
        if (hasIkansakiAreaCode()) {
            areaCodes.add(ikansakiAreaCode);
        }
        return areaCodes;
    }
    
    // ==================== DatabaseMappable実装 ====================
    
    /**
     * DTOの核心フィールドをデータベースフィールドのMapに変換
     * T_JINENDO_KKK表のフィールドにマッピング
     * 固定値フィールドの注入は親インターフェースのテンプレートメソッドで統一処理
     *
     * @param isInsert 挿入操作であるかどうか
     * @return データベースフィールドのMap（固定値フィールドは含まない）
     */
    @Override
    public Map<String, Object> toDatabaseFieldsCore(boolean isInsert) {
        Map<String, Object> fields = new HashMap<>();
        
        // 年度（次年度を自動設定）
        fields.put("NENDO", DateUtil.getNextFiscalYear());
        
        // 複合主キーフィールド
        fields.put("SSNKN_TNCD", saisaknKanriTaniCode);
        fields.put("GROUP_CODE", groupCode);
        
        // 基本フィールド
        fields.put("AREA_CODE", areaCode);
        fields.put("UNIT_CODE", unitCode);
        fields.put("CTGRY_CODE", categoryCode);
        fields.put("KIGYO_CODE", kigyoCode);
        fields.put("GYT_SHK_NO", gyotaiShukei);
        
        // 任意フィールド（nullチェック）
        fields.put("SUB_CTGRY_CODE", subCategoryCode);
        fields.put("TNTSH_MEI", tantoshaName);
        fields.put("GYTM_KANJI", gyotaiName);
        fields.put("HNKG_TRKM_KUBUN", henkogoTorikumiKubun);
        fields.put("SSN_KANRI_TNM_KANJI", saisaknKanriTaniName);
        
        // 移管先フィールド
        fields.put("IKNSK_AREA_CODE", ikansakiAreaCode);
        fields.put("IKNSK_GROUP_CODE", ikansakiGroupCode);
        fields.put("IKNSK_UNIT_CODE", ikansakiUnitCode);
        
        // 適用フィールド（業務ロジック適用）
        fields.put("TKY_AREA_CODE", getApplicableAreaCode());
        fields.put("TKY_GROUP_CODE", getApplicableGroupCode());
        fields.put("TKY_UNIT_CODE", getApplicableUnitCode());

        // プログラムIDをFunctionUtilから取得
        String programId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE,
                BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE).getFunctionId();

        // 自動生成フィールド
        Date currentTime = new Date();
        String currentTimeStr = DateUtil.formatDateTime(currentTime);

        if (isInsert) {
            // 挿入時のみ設定するフィールド
            fields.put("TRK_PRGRM_ID", programId);
            fields.put("VRSN", 1);
            fields.put("RCRD_TRK_NCHJ", currentTimeStr);
        }

        // 更新時は常に設定
        fields.put("KSHN_PRGRM_ID", programId);
        fields.put("RCRD_KSHN_NCHJ", currentTimeStr);

        // 注意：固定値フィールド（SYSTM_UNYO_KIGYO_CODE等）は
        // ImportOptionsのadditionalFieldsで統一処理される
        
        return fields;
    }
}
