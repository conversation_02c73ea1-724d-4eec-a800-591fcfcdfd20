package com.ms.bp.domain.file.model;

import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * エクスポートジョブステータスエンティティクラス
 * ファイル処理領域のエクスポート作業状態管理モデル
 * PostgreSQLデータベースのT_DWNLD_RRK（ダウンロード履歴テーブル）に対応
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExportJobStatus {

    // 主キー
    /** 履歴番号 */
    private Long rrkBango;

    // 基本情報
    /** システム運用企業コード */
    private String systmUnyoKigyoCode;

    /** 社員コード */
    private String shainCode;

    /** ファイル種別 */
    private String fileShbts;

    /** ファイル種別名（SQL内でCASE WHEN変換済み） */
    private String fileShbtsName;

    /** エリア */
    private String area;

    /** エリア名（連表查询で取得、SQL内でCASE WHEN変換済み） */
    private String areaName;

    /** 本社場所区分 */
    private String hnshBashoKubun;

    /** 本社場所区分名（SQL内でCASE WHEN変換済み） */
    private String hnshBashoKubunName;

    /** データ区分 */
    private String dataKubun;

    /** データ区分名（SQL内でCASE WHEN変換済み） */
    private String dataKubunName;

    /** カテゴリー区分 */
    private String ctgryKubun;
    /** カテゴリー区分名 */
    private String ctgryKubunName;

    // 処理日時
    /** ファイル作成開始日時 */
    private String fileSksKshNchj;

    /** ファイル作成完了日時 */
    private String fileSksKnrNchj;

    // ステータス・結果
    /** ステータス (0:処理中、1:完了、2:一部失敗、3:失敗、4:システムエラー) */
    private String stts;

    /** ステータス名（SQL内でCASE WHEN変換済み） */
    private String sttsName;

    /** ZIPサイズ */
    private String zipFileSize;

    /** ZIPファイル名 */
    private String zipFileMei;

    // 登録情報
    /** 登録プログラムID */
    private String trkPrgrmId;

    /** 登録システム運用企業コード */
    private String trkSystmUnyoKigyoCode;

    /** 登録社員コード */
    private String trkShainCode;

    // 更新情報
    /** 更新プログラムID */
    private String kshnPrgrmId;

    /** 更新システム運用企業コード */
    private String kshnSystmUnyoKigyoCode;

    /** 更新社員コード */
    private String kshnShainCode;

    // システム情報
    /** バージョン */
    private Integer vrsn;

    /** レコード登録日時 */
    private String rcrdTrkNchj;

    /** レコード更新日時 */
    private String rcrdKshnNchj;

    /**
     * 初期化処理 - 監査フィールドを自動設定
     * @param functionId 機能ID（nullの場合はデフォルト値を使用）
     * @return 初期化済みのインスタンス
     */
    public ExportJobStatus init(String functionId) {
        String currentDateTime = DateUtil.getCurrentDateTimeString();
        String programId = (functionId != null && !functionId.trim().isEmpty())
                ? functionId : "SYSTEM";

        // 処理開始日時
        this.fileSksKshNchj = currentDateTime;

        // プログラムID
        this.trkPrgrmId = programId;
        this.kshnPrgrmId = programId;

        // 監査用企業コード・社員コード
        this.trkSystmUnyoKigyoCode = this.systmUnyoKigyoCode;
        this.trkShainCode = this.shainCode;
        this.kshnSystmUnyoKigyoCode = this.systmUnyoKigyoCode;
        this.kshnShainCode = this.shainCode;

        // タイムスタンプ
        this.rcrdTrkNchj = currentDateTime;
        this.rcrdKshnNchj = currentDateTime;
        this.stts = BusinessConstants.BATCH_STATUS_PROCESSING_CODE; // 処理中
        this.vrsn = 1;

        return this;
    }

    /**
     * ダウンロード可能かどうか
     */
    public boolean isDownloadable() {
        return zipFileMei != null && !zipFileMei.isEmpty();
    }

    /**
     * 処理完了時の統計情報を更新
     * @param fileName ZIPファイル名
     * @param fileSize ZIPファイルサイズ
     */
    public void updateCompletionStats(String fileName, String fileSize) {
        String currentDateTime = DateUtil.getCurrentDateTimeString();
        this.zipFileMei = fileName;
        this.zipFileSize = fileSize;
        this.stts = BusinessConstants.BATCH_STATUS_COMPLETED_CODE; // 完了
        this.fileSksKnrNchj = currentDateTime;
        this.rcrdKshnNchj = currentDateTime;
        this.vrsn = this.vrsn + 1;
    }

    /**
     * 一部失敗時の更新
     * @param fileName ZIPファイル名
     * @param fileSize ZIPファイルサイズ
     */
    public void updatePartialFailureStatus(String fileName, String fileSize) {
        String currentDateTime = DateUtil.getCurrentDateTimeString();
        this.zipFileMei = fileName;
        this.zipFileSize = fileSize;
        this.stts = BusinessConstants.BATCH_STATUS_PARTIALLY_FAILED_CODE; // 一部失敗
        this.fileSksKnrNchj = currentDateTime;
        this.rcrdKshnNchj = currentDateTime;
        this.vrsn = this.vrsn + 1;
    }

    /**
     * 処理失敗時の更新
     * @param fileName ZIPファイル名（失敗時でもZIPファイルが作成される場合）
     * @param fileSize ZIPファイルサイズ（失敗時でもZIPファイルが作成される場合）
     * @param isSystemError システムエラーかどうか
     */
    public void updateFailureStatus(String fileName, String fileSize, boolean isSystemError) {
        String currentDateTime = DateUtil.getCurrentDateTimeString();
        this.zipFileMei = fileName;
        this.zipFileSize = fileSize;
        this.stts = isSystemError ? BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE : BusinessConstants.BATCH_STATUS_FAILED_CODE; // 4:システムエラー、3:失敗
        this.fileSksKnrNchj = currentDateTime;
        this.rcrdKshnNchj = currentDateTime;
        this.vrsn = this.vrsn + 1;
    }

}
