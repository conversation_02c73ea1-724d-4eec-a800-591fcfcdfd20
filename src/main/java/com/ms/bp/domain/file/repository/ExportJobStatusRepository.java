package com.ms.bp.domain.file.repository;

import com.ms.bp.domain.file.model.ExportJobStatus;
import java.util.List;

/**
 * エクスポートジョブステータスリポジトリ
 * ファイル処理領域のエクスポートジョブステータス永続化を抽象化
 * T_DWNLD_RRK（ダウンロード履歴テーブル）に対応
 */
public interface ExportJobStatusRepository {

    /**
     * エクスポートジョブステータスを保存
     * @param jobStatus ジョブステータス
     */
    void save(ExportJobStatus jobStatus);

    /**
     * エクスポートジョブステータスを更新
     * @param jobStatus ジョブステータス
     */
    void update(ExportJobStatus jobStatus);

    /**
     * 履歴番号でエクスポートジョブステータスを取得
     * @param rrkBango 履歴番号
     * @return エクスポートジョブステータス
     */
    ExportJobStatus findByRrkBango(Long rrkBango);

    /**
     * 社員コードでエクスポートジョブステータスリストを取得
     * M_SOSHIKIAREAMSTテーブルと連表查询してエリア名も一緒に取得し、
     * SQL内でCASE WHEN文によりエリア名変換を実行する
     *
     * @param shainCode 社員コード
     * @param limit 取得件数上限
     * @param offset オフセット
     * @return エクスポートジョブステータスリスト（エリア名変換済み）
     */
    List<ExportJobStatus> findByShainCode(String shainCode, String systemOperationCompanyCode, int limit, int offset);
}
