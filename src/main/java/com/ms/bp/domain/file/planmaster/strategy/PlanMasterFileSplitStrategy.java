package com.ms.bp.domain.file.planmaster.strategy;

import com.ms.bp.application.data.FileExportOrchestrator.ExportTask;
import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.repository.impl.AreaCodeRepositoryImpl;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.ExportFileNameUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 次年度計画マスタ専用ファイル分割戦略
 * 単一ファイル生成とSKSA特殊エリア処理を実装
 * 主な機能：
 * 1. 単一ファイル生成（エリア数に関係なく1ファイル）
 * 2. SKSA→実際エリアコード変換処理
 * 3. 時刻ベースファイル名生成
 */
public class PlanMasterFileSplitStrategy implements FileSplitStrategy {
    private static final Logger logger = LoggerFactory.getLogger(PlanMasterFileSplitStrategy.class);



    @Override
    public List<ExportTask> createExportTasks(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("次年度計画マスタエクスポートタスク作成開始");

        // SKSA特殊エリア処理を実行
        ExportRequest processedRequest = processSksaAreaCode(exportRequest);

        // ファイル名を生成
        String fileName = ExportFileNameUtil.generateDefaultFileName(BusinessConstants.FILE_NAME_PLAN_MASTER);

        // SQL戦略を決定
        String sqlStrategy = BusinessConstants.PLAN_MASTER_DEFAULT;

        // 単一エクスポートタスクを作成
        ExportTask task = new ExportTask("plan_master", processedRequest, sqlStrategy, fileName);

        logger.info("次年度計画マスタエクスポートタスク作成完了: ファイル名={}, SQL戦略={}",
                fileName, sqlStrategy);

        return List.of(task);
    }

    /**
     * SKSA特殊エリアコード処理
     * SKSAが含まれている場合、データベースから実際のエリアコードを取得して置換
     * 
     * @param originalRequest 元のエクスポートリクエスト
     * @return 処理済みエクスポートリクエスト
     */
    private ExportRequest processSksaAreaCode(ExportRequest originalRequest) {
        List<String> originalAreaList = originalRequest.getArea();

        // SKSAが含まれていない場合はそのまま返す
        if (!originalAreaList.contains(BusinessConstants.PROFIT_MANAGEMENT_PLANNING_AREA_CODE)) {
            logger.debug("SKSAが含まれていないため、SKSA処理をスキップします");
            return originalRequest;
        }
        
        logger.debug("SKSA特殊エリア処理開始: 元エリアリスト={}", originalAreaList);

        // データベースから実際のエリアコードを取得（DateUtilから次年度を取得）
        List<String> actualAreaCodes = LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
            AreaCodeRepositoryImpl areaCodeRepository = new AreaCodeRepositoryImpl(jdbcTemplate);
            return areaCodeRepository.findAreaCodesByNendo(
                    DateUtil.getNextFiscalYear(), BusinessConstants.SYSTEM_OPERATION_COMPANY_CODE);
        });

        // 新しいエリアリストを作成（SKSAを実際のエリアコードで置換）
        List<String> processedAreaList = new ArrayList<>();
        for (String area : originalAreaList) {
            if (BusinessConstants.PROFIT_MANAGEMENT_PLANNING_AREA_CODE.equals(area)) {
                // SKSAを実際のエリアコードで置換
                processedAreaList.addAll(actualAreaCodes);
            } else {
                // SKSA以外はそのまま追加
                processedAreaList.add(area);
            }
        }

        // 重複を除去
        List<String> uniqueAreaList = new ArrayList<>(new LinkedHashSet<>(processedAreaList));
        
        // 新しいExportRequestを作成
        ExportRequest processedRequest = new ExportRequest();
        processedRequest.setDataType(originalRequest.getDataType());
        processedRequest.setArea(uniqueAreaList);
        processedRequest.setHnshBashoKubun(originalRequest.getHnshBashoKubun());
        processedRequest.setDataKubun(originalRequest.getDataKubun());
        
        logger.debug("SKSA特殊エリア処理完了: 処理後エリアリスト={}", uniqueAreaList);
        
        return processedRequest;
    }
}
