package com.ms.bp.domain.file.base;

import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.application.data.strategy.DataAccessStrategy;
import com.ms.bp.application.data.strategy.DataAccessStrategyManager;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.io.model.ExportResult;
import com.ms.bp.shared.common.io.options.ExportOptions;

import java.util.Map;
import java.util.Objects;

/**
 * エクスポートサービスの抽象クラス
 * エクスポート処理の共通ロジックを定義
 */
public abstract class AbstractExportService<T> extends AbstractDataService<ExportRequest, ExportResult> {

    protected final DataAccessStrategyManager sqlStrategyManager;

    public AbstractExportService() {
        this.sqlStrategyManager = new DataAccessStrategyManager();
        // サブクラスでSQL戦略を登録
        registerSqlStrategies();
    }

    /**
     * データ格式化処理（オプション、サブクラスで必要に応じてオーバーライド）
     * データベースから取得したMapデータを最終出力用に格式化する
     *
     * @param rawData データベースから取得した生データ
     * @return 格式化されたデータ
     */
    protected Map<String, Object> formatData(Map<String, Object> rawData) {
        // デフォルトは何も変更しない
        return rawData;
    }

    /**
     * パラメータを考慮したエクスポートオプションを構築
     * sqlStrategyKeyとfileNameを統一的に処理
     * ExportTask情報がある場合はタスク固有の設定を適用
     * サブクラスは具体的なオプション構築のみに集中できる
     */
    protected ExportOptions buildExportOptions(Map<String, Object> params) {
        // パラメータからsqlStrategyKey、fileName、ExportTaskを取得
        String sqlStrategyKey = (String) params.get("sqlStrategyKey");
        String customFileName = (String) params.get("fileName");
        Object exportTask = params.get("exportTask"); // 新規追加：ExportTaskオブジェクト

        logger.debug("エクスポートオプション構築: SQL戦略={}, ファイル名={}, タスク={}",
                    sqlStrategyKey, customFileName, exportTask != null ? exportTask.toString() : "null");

        // ExportTask情報がある場合は新しいメソッドを使用、ない場合は既存メソッドを使用
        // これにより向後兼容性を保持しつつ、新機能を提供
        ExportOptions baseOptions = exportTask != null ?
            buildExportOptionsWithStrategy(sqlStrategyKey, exportTask) :
            buildExportOptionsWithStrategy(sqlStrategyKey);

        // fileName統一処理：AbstractExportServiceで統一的に設定
        return applyCommonSettings(baseOptions, customFileName);
    }

    /**
     * 共通設定を適用（fileName統一処理）
     * @param baseOptions サブクラスで構築された基本オプション
     * @param customFileName カスタムファイル名
     * @return 共通設定が適用されたExportOptions
     */
    private ExportOptions applyCommonSettings(ExportOptions baseOptions, String customFileName) {
        // 既存のオプションをベースに、fileNameのみ統一設定で上書き
        return ExportOptions.builder()
                .format(baseOptions.getFormat())
                .delimiter(baseOptions.getDelimiter())
                .includeHeader(baseOptions.isIncludeHeader())
                .columns(baseOptions.getColumns())
                .entityMapper(baseOptions.getEntityMapper())
                .batchSize(baseOptions.getBatchSize())
                .queryTimeout(baseOptions.getQueryTimeout())
                .countTotal(baseOptions.isCountTotal())
                .threadCount(baseOptions.getThreadCount())
                .fieldHeaderMapping(baseOptions.getFieldHeaderMapping())
                .enableFieldMapping(baseOptions.isEnableFieldMapping())
                .fileName(customFileName) // 統一的に設定
                .build();
    }


    /**
     * SQL戦略とファイル名を考慮したエクスポートオプションを構築
     * サブクラスで実装する具体的なオプション構築メソッド
     *
     * @param sqlStrategyKey SQL戦略キー（nullの場合はデフォルト戦略）
     * @return エクスポートオプション
     */
    protected abstract ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey);

    /**
     * SQL戦略とExportTaskを考慮したエクスポートオプションを構築（新規追加）
     * ExportTask毎に異なるCSVヘッダー設定を可能にする拡張メソッド
     * サブクラスで必要に応じてオーバーライドし、タスク固有の設定を実装
     *
     * @param sqlStrategyKey SQL戦略キー（nullの場合はデフォルト戦略）
     * @param task エクスポートタスク（ヘッダー設定の参考情報）
     * @return エクスポートオプション
     */
    protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey, Object task) {
        // デフォルト実装：既存メソッドを呼び出して向後兼容性を保持
        // サブクラスでオーバーライドしてタスク固有の設定を実装可能
        return buildExportOptionsWithStrategy(sqlStrategyKey);
    }

    /**
     * SQL戦略を登録（サブクラスで実装）
     */
    protected abstract void registerSqlStrategies();

    /**
     * 戦略を使用してSQLを構築
     * @param exportRequest エクスポートリクエスト
     * @param strategyKey 戦略キー
     * @param userInfo ユーザー情報
     * @return SQLクエリとパラメータ
     */
    protected DataAccessStrategy.SqlWithParams buildQueryWithStrategy(ExportRequest exportRequest, String strategyKey, UserInfo userInfo) {
        DataAccessStrategy strategy = sqlStrategyManager.getStrategy(strategyKey);

        if (strategy != null) {
            return strategy.buildQuery(exportRequest, userInfo);
        }

        // フォールバック：戦略が見つからない場合
        throw new UnsupportedOperationException(
            "SQL戦略 '" + strategyKey + "' が見つかりません。");
    }

    /**
     * データ展開処理器を取得（サブクラスで実装）
     * データ展開が不要な場合はnullを返す
     *
     * @return データ展開処理器、または展開不要の場合はnull
     */
    protected abstract DataExpander getDataExpander();

    /**
     * ファイル分割戦略を取得（サブクラスで実装）
     * 分割が不要な場合はデフォルト戦略を返す
     *
     * @return ファイル分割戦略
     */
    public abstract FileSplitStrategy getSplitStrategy() ;

    @Override
    protected void validateParameters(ExportRequest exportRequest, Map<String, Object> params) {
        // 基本的な検証はここで実施
        // 詳細な検証は各サブクラスで実装
    }

    @Override
    protected ExportResult doExecute(ExportRequest exportRequest, Map<String, Object> params) throws Exception {
        // SQL構築（戦略対応）
        DataAccessStrategy.SqlWithParams sqlWithParams;
        String strategyKey = (String) params.get("sqlStrategyKey");
        UserInfo userInfo = (UserInfo) params.get("userInfo");

        // ExportTaskから戦略が指定された場合
        // フォールバック：デフォルト戦略を使用
        sqlWithParams = buildQueryWithStrategy(exportRequest, Objects.requireNonNullElse(strategyKey, "DEFAULT"), userInfo);

        // オプション構築（パラメータを考慮）
        ExportOptions options = buildExportOptions(params);

        // データ展開処理器を取得
        DataExpander expander = getDataExpander();

        // 展開コンテキストを作成（expanderがnullの場合はnull）
        ExpansionContext expansionContext = expander != null ?
            createExpansionContext(exportRequest, params, options) : null;

        // 統一されたエクスポート実行（データ展開の有無を自動判定）
        return facade.exportData(
                sqlWithParams.sql(),
                sqlWithParams.params(),
                this::formatData,
                options,
                expander,
                expansionContext
        );
    }



    /**
     * 展開コンテキストを作成
     * サブクラスで必要に応じてオーバーライド可能
     *
     * @param exportRequest エクスポートリクエスト
     * @param params リクエストパラメータ
     * @param options エクスポートオプション
     * @return 展開コンテキスト
     */
    protected ExpansionContext createExpansionContext(ExportRequest exportRequest,
                                                    Map<String, Object> params,
                                                    ExportOptions options) {
        return ExpansionContext.builder()
                .requestParams(params)
                .exportRequest(exportRequest)
                .targetColumns(options.getColumns())
                .build();
    }


    @Override
    protected void logStart(ExportRequest exportRequest, Map<String, Object> params) {
        logger.info("エクスポート開始: エクスポートリクエスト={}, パラメータ={}",
                exportRequest, params);
    }

    @Override
    protected void logEnd(ExportResult result, long executionTime) {
        logger.info("エクスポート完了: エクスポート件数={}, 実行時間={}ms",
                result.getExportedCount(),
                executionTime
        );
    }

    @Override
    protected String getErrorMessage() {
        return "エクスポート中にエラーが発生しました";
    }

}