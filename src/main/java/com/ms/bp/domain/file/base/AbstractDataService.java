package com.ms.bp.domain.file.base;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.shared.common.io.facade.DataImportExportFacade;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.Map;
import java.util.Optional;

/**
 * データインポート・エクスポートサービスの抽象基底クラス
 * 共通処理とテンプレートメソッドを定義
 */
public abstract class AbstractDataService<T, R> {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final DataImportExportFacade facade;
    protected final S3Service s3Service;
    protected Context lambdaContext;

    protected AbstractDataService() {
        this.facade = new DataImportExportFacade(null);
        this.s3Service = new S3Service();
    }

    /**
     * Lambdaコンテキストを設定
     * @param context Lambdaコンテキスト
     */
    public void setLambdaContext(Context context) {
        this.lambdaContext = context;
        // コンテキスト情報をMDCに追加
        Optional.ofNullable(context)
                .ifPresent(ctx -> MDC.put("awsRequestId", ctx.getAwsRequestId()));
    }

    /**
     * 処理実行のテンプレートメソッド
     * @param input 入力データ
     * @param params パラメータ
     * @return 処理結果
     */
    public final R execute(T input, Map<String, Object> params) {
        try {
            // 前処理
            preProcess(input, params);

            // パラメータ検証
            validateParameters(input, params);

            // ログ出力
            logStart(input, params);

            // メイン処理
            long startTime = System.currentTimeMillis();
            R result = doExecute(input, params);
            long executionTime = System.currentTimeMillis() - startTime;

            // 後処理
            postProcess(result, executionTime);

            // ログ出力
            logEnd(result, executionTime);

            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("処理中にエラーが発生しました: {}", e.getMessage(), e);
            throw new ServiceException(
                    GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    getErrorMessage() + ": " + e.getMessage()
            );
        } finally {
            // MDCをクリア
            MDC.clear();
        }
    }

    /**
     * 前処理（サブクラスでオーバーライド可能）
     */
    protected void preProcess(T input, Map<String, Object> params) {
        // デフォルトは何もしない
    }

    /**
     * 後処理（サブクラスでオーバーライド可能）
     */
    protected void postProcess(R result, long executionTime) {
        // デフォルトは何もしない
    }

    /**
     * パラメータ検証（サブクラスで実装）
     */
    protected abstract void validateParameters(T input, Map<String, Object> params);

    /**
     * メイン処理（サブクラスで実装）
     */
    protected abstract R doExecute(T input, Map<String, Object> params) throws Exception;

    /**
     * 開始ログ出力（サブクラスで実装）
     */
    protected abstract void logStart(T input, Map<String, Object> params);

    /**
     * 終了ログ出力（サブクラスで実装）
     */
    protected abstract void logEnd(R result, long executionTime);

    /**
     * エラーメッセージ取得（サブクラスで実装）
     */
    protected abstract String getErrorMessage();

    /**
     * リソースクリーンアップ
     */
    public void close() {
        try {
            s3Service.close();
        } catch (Exception e) {
            logger.error("リソースのクローズ中にエラーが発生しました", e);
        }
    }
}