package com.ms.bp.domain.file.base;

import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.model.ImportResult;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DataValidator;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * インポートサービスの抽象クラス
 * インポート処理の共通ロジックを定義
 * パフォーマンス最適化：DatabaseMappableを実装するDTOのみを受け入れ
 */
public abstract class AbstractImportService<T extends DatabaseMappable> extends AbstractDataService<InputStream, ImportResult> {

    /**
     * DTOクラスを取得（サブクラスで実装）
     */
    protected abstract Class<T> getDTOClass();



    /**
     * インポートオプションを構築（サブクラスで実装）
     */
    protected abstract ImportOptions buildImportOptions();

    /**
     * データタイプを取得（サブクラスで実装）
     */
    protected abstract String getDataType();

    /**
     * データ検証器を取得（サブクラスで実装）
     * @return データ検証器、またはnull（デフォルト検証を使用する場合）
     */
    protected abstract DataValidator getDataValidator();

    @Override
    protected void validateParameters(InputStream input, Map<String, Object> params) {
        if (input == null) {
            throw new ServiceException(
                    GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "入力ストリームが指定されていません"
            );
        }
    }

    @Override
    protected ImportResult doExecute(InputStream input, Map<String, Object> params) throws Exception {
        // オプション構築
        ImportOptions options = buildImportOptions();

        // パラメータから追加設定を適用
        applyAdditionalOptions(options, params);

        // データ検証器を取得
        DataValidator validator = getDataValidator();

        // インポート実行（検証器は必須）
        return facade.importData(
                input,
                getDTOClass(),
                options,
                validator
        );
    }

    /**
     * 追加オプションを適用（必要に応じてサブクラスでオーバーライド）
     */
    protected void applyAdditionalOptions(ImportOptions options, Map<String, Object> params) {
        // デフォルトは何もしない
    }

    @Override
    protected void logStart(InputStream input, Map<String, Object> params) {
        logger.info("{}インポート開始: パラメータ={}", getDataType(), params);
    }

    @Override
    protected void logEnd(ImportResult result, long executionTime) {
        logger.info("{}インポート完了: 挿入={}, 更新={}, エラー={}, 実行時間={}ms",
                getDataType(),
                result.getInsertedCount(),
                result.getUpdatedCount(),
                result.getFailedCount(),
                executionTime
        );

        // エラーファイル情報があれば出力
        if (result.getStatistics().containsKey("errorFileUrl")) {
            logger.info("エラーファイル: {}", result.getStatistics().get("errorFileUrl"));
        }
    }

    @Override
    protected String getErrorMessage() {
        return getDataType() + "インポート中にエラーが発生しました";
    }

    /**
     * 公開APIメソッド
     */
    public ImportResult importData(InputStream inputStream) {
        return execute(inputStream, new HashMap<>());
    }
}