package com.ms.bp.domain.permission.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 個人権限設定マスタ領域モデル
 * 個人権限に関するビジネスロジックを含む富ドメインモデル
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonalPermission {
    
    /**
     * 権限コード
     */
    private String permissionCode;
    
    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;
    
    /**
     * 社員コード
     */
    private String shainCode;
    
    /**
     * ファイル種類コード
     */
    private String fileTypeCode;

    /**
     * 操作区分 (1:アップロード 2:ダウンロード)
     */
    private String operationDivision;

    /**
     * 判定コード
     */
    private String hantCode;


    /**
     * 許可フラグ (0:許可 1:禁止)
     */
    private String permissionFlag;

}
