package com.ms.bp.domain.permission.model;

import com.ms.bp.domain.master.model.AreaInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * ユーザー権限情報値オブジェクト
 * ユーザーの権限情報を表現する値オブジェクト
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPermissionInfo {
    
    /**
     * 権限コード
     */
    private String permissionCode;
    
    /**
     * ファイル種類コード
     */
    private String fileTypeCode;
    
    /**
     * 操作区分 (1:アップロード 2:ダウンロード)
     */
    private String operationDivision;
    
    /**
     * エリアパターン(1:本社 2:エリア)
     */
    private String areaPattern;

    /**
     * 判定コード
     */
    private String hantCode;

}
