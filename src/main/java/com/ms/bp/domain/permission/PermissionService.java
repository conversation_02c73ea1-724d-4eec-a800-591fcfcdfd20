package com.ms.bp.domain.permission;

import com.ms.bp.domain.permission.model.*;
import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 権限ドメインサービス
 * 権限に関するビジネスロジックを実装する
 */
public class PermissionService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionService.class);

    // ファイルタイプマッピング
    private static final Map<String, String> FILE_TYPE_MAPPING = Map.of(
            "001", BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE,
            "002", BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE,
            "003", BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE,
            "004", BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE
    );
    // 権限コード定数
    private static final String DOWNLOAD_PREFIX = "DL";
    private static final String UPLOAD_PREFIX = "UL";
    private static final char HEAD_OFFICE_MARKER = 'H';
    private static final char AREA_MARKER = 'A';
    private final PermissionRepository permissionRepository;

    public PermissionService(PermissionRepository permissionRepository) {
        this.permissionRepository = permissionRepository;
    }

    /**
     * ユーザーの全権限を取得（初期化処理用）
     *
     * @param userInfo ユーザー情報
     * @return ユーザーの権限リスト
     */
    public List<UserPermissionInfo> getUserPermissions(UserInfo userInfo) {
        logger.info("ユーザー権限取得開始: 社員コード={}", userInfo.getShainCode());

        try {
            // 共通権限を取得
            var commonPermissions = permissionRepository.findCommonPermissions(
                            userInfo.getSystemOperationCompanyCode(),
                            userInfo.getUnitCode(),
                            userInfo.getAreaCode()
                    ).stream()
                    .map(p -> parsePermissionCode(p.getPermissionCode(),p.getHantCode()))
                    .toList();

            // 個人権限を取得
            var personalPermissions = permissionRepository.findPersonalPermissions(
                            userInfo.getShainCode(),
                            userInfo.getSystemOperationCompanyCode()
                    ).stream()
                    .map(p -> parsePermissionCode(p.getPermissionCode(),p.getHantCode()))
                    .toList();

            // 共通権限と個人権限を統合（個人権限で上書き）
            var allPermissionMap = Stream.concat(
                    commonPermissions.stream(),
                    personalPermissions.stream()
            ).collect(Collectors.toMap(
                    UserPermissionInfo::getPermissionCode,
                    Function.identity(),
                    (existing, replacement) -> replacement
            ));

            var result = deduplicatePermissions(new ArrayList<>(allPermissionMap.values()));

            logger.info("ユーザー権限取得完了: 社員コード={}, 権限数={}",
                    userInfo.getShainCode(), result.size());

            return result;

        } catch (Exception e) {
            logger.error("ユーザー権限取得中にエラーが発生しました: 社員コード={}", userInfo.getShainCode(), e);
            throw new ServiceException(
                    GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "ユーザー権限取得処理でエラーが発生しました: " + e.getMessage()
            );
        }
    }

    /**
     * 権限リストの重複を削除
     * 3桁目が'H'（本社）と'A'（エリア）の権限が共存する場合、'A'の権限を削除
     */
    public static List<UserPermissionInfo> deduplicatePermissions(List<UserPermissionInfo> permissions) {
        var allCodes = permissions.stream()
                .map(UserPermissionInfo::getPermissionCode)
                .collect(Collectors.toSet());

        return permissions.stream()
                .filter(info -> {
                    var code = info.getPermissionCode();
                    // 3桁目が'A'かつ対応する'H'権限が存在する場合は除外
                    if (code.charAt(2) == AREA_MARKER) {
                        var headOfficeCode = code.substring(0, 2) + HEAD_OFFICE_MARKER + code.substring(3);
                        return !allCodes.contains(headOfficeCode);
                    }
                    return true;
                }).toList();
    }

    /**
     * 権限コードを解析し、UserPermissionInfoオブジェクトを生成
     */
    private static UserPermissionInfo parsePermissionCode(String permissionCode, String hantCode) {
        var info = new UserPermissionInfo();
        info.setPermissionCode(permissionCode);

        // 1-2桁目: 操作区分
        var operation = permissionCode.substring(0, 2);
        info.setOperationDivision(switch (operation) {
            case DOWNLOAD_PREFIX -> BusinessConstants.OPERATION_DOWNLOAD_CODE;
            case UPLOAD_PREFIX -> BusinessConstants.OPERATION_UPLOAD_CODE;
            default -> "";
        });

        // 3桁目: 対象所属区分
        var areaPattern = permissionCode.charAt(2);
        info.setAreaPattern(switch (areaPattern) {
            case HEAD_OFFICE_MARKER -> BusinessConstants.AFFILIATION_HEAD_OFFICE;
            case AREA_MARKER -> BusinessConstants.AFFILIATION_AREA;
            default -> "";
        });

        // 4-6桁目: ファイル種類コード
        var fileType = permissionCode.substring(3, 6);
        info.setFileTypeCode(FILE_TYPE_MAPPING.getOrDefault(fileType, ""));
        // 判定コード
        info.setHantCode(hantCode);
        return info;
    }
}