package com.ms.bp.domain.master.repository;

import com.ms.bp.domain.master.model.AreaInfo;
import java.util.List;

/**
 * エリアコードリポジトリ
 * 次年度計画マスタ領域のエリアコード永続化を抽象化
 * M_SK_KKK_AREA（採算管理単位計画策定エリアマスタ）に対応
 */
public interface AreaCodeRepository {

    /**
     * 年度とシステム運用企業コードでエリアコードリストを取得
     * SKSA（採算管理単位計画策定エリア）に対応する実際のエリアコードを取得
     *
     * @param nendo 年度（YYYY形式）
     * @param systemOperationCompanyCode システム運用企業コード
     * @return エリアコードリスト（エリア表示順でソート済み）
     */
    List<String> findAreaCodesByNendo(String nendo, String systemOperationCompanyCode);

    /**
     * 年度とシステム運用企業コードでエリア情報リストを取得
     * M_SK_KKK_AREAとM_SOSHIKIAREAMSTを関連付けてエリアコードとエリア名称を取得
     *
     * @param nendo 年度（YYYY形式）
     * @param systemOperationCompanyCode システム運用企業コード
     * @return エリア情報リスト（エリア表示順でソート済み）
     */
    List<AreaInfo> findAreaInfosByNendo(String nendo, String systemOperationCompanyCode);

    /**
     * 見通しエクスポート_年度とシステム運用企業コードちエリアコードでエリア情報リストを取得
     * エリアコードが重複した場合は、重複削除要
     * SKSAが含まれている場合、データベースから実際のエリアコードを取得して置換
     *
     * @param nendo 年度（YYYY形式）
     * @param systemOperationCompanyCode システム運用企業コード(SKSA)
     * @param areaCodes エリアコードリスト
     * @return 処理済みエリアコードリスト
     */
    List<AreaInfo> findAreaInfosForMsth(String nendo, String systemOperationCompanyCode, List<String> areaCodes);
}
