package com.ms.bp.domain.master.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * ユーザー基本情報値オブジェクト
 * 社員マスタ、ユニットマスタ、グループマスタ、組織エリアマスタから取得する業務情報を表現する値オブジェクト
 * M_SHAINMST、M_UNITMST、M_GROUPMST、M_SOSHIKIAREAMSTの関連データを保持
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserBasicInfo {

    /**
     * ユニットコード（5桁）
     * M_SHAINMST.UNIT_CODE
     */
    private String unitCode;

    /**
     * 役職区分コード（2桁）
     * M_SHAINMST.YKSHK_KUBUN
     */
    private String positionCode;

    /**
     * エリアコード（4桁）
     * M_GROUPMST.AREA_CODE
     */
    private String areaCode;

    /**
     * エリア名称（漢字）
     * M_SOSHIKIAREAMST.SUB_AREA_MEI_KANJI
     */
    private String areaName;

    /**
     * 社員コード（6桁）
     * M_SHAINMST.SHAIN_CODE
     */
    private String shainCode;

    /**
     * システム運用企業コード（6桁）
     * M_SHAINMST.SYSTM_UNYO_KIGYO_CODE
     */
    private String systemOperationCompanyCode;


    /**
     * グループコード
     * M_GROUPMST.GROUP_CODE
     */
    private String groupCode;
}
