package com.ms.bp.domain.master.repository;

import com.ms.bp.domain.file.model.AnnounceMessage;
import com.ms.bp.domain.file.model.ExportJobStatus;

import java.util.List;

/**
 * アナウンスメッセージリポジトリを取得（シングルトン）
 * @return アナウンスメッセージリポジトリ
 */
public interface AnnounceMessageRepository {

    /**
     * メッセージ区分でアナウンスメッセージを取得
     * @param msgkbn メッセージ区分
     * @return アナウンスメッセージ
     */
    List<AnnounceMessage> findByMsgKubun(String msgkbn);
}