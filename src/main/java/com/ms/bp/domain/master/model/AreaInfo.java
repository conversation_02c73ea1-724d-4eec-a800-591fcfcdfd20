package com.ms.bp.domain.master.model;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * エリア情報値オブジェクト
 * エリアコードとエリア名称の組み合わせを表現する値オブジェクト
 * M_SK_KKK_AREAとM_SOSHIKIAREAMSTの関連データを保持
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AreaInfo {
    
    /**
     * エリアコード（4桁）
     * M_SK_KKK_AREA.area_code
     */
    private String areaCode;
    
    /**
     * エリア名短縮漢字
     * M_SOSHIKIAREAMST.AREA_MEI_TNSHK_KANJI
     */
    private String areaName;

}
