package com.ms.bp.domain.master.repository;

/**
 * グループマスタリポジトリ
 * グループマスタ領域のデータ永続化を抽象化
 * M_GROUPMST（グループマスタ）に対応
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public interface GroupMasterRepository {

    /**
     * グループコードでグループマスタの存在チェック
     * 指定されたグループコードがグループマスタに存在するかを確認する
     * 
     * @param groupCode グループコード（4桁）
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsByGroupCode(String groupCode);
}
