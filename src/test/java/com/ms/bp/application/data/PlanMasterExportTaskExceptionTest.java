package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.ExportJobStatusService;
import com.ms.bp.domain.file.model.ExportJobStatus;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ms.bp.application.data.strategy.DataAccessStrategy;
import com.ms.bp.domain.file.planmaster.PlanMasterExportService;
import com.ms.bp.domain.file.planmaster.strategy.PlanMasterDataStrategy;
import com.ms.bp.interfaces.dto.request.WorkerPayload;

import java.io.IOException;
import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 次年度計画マスタエクスポート機能の異常テスト
 * 真実の導出フローを実行し、中間環節で異常を制造してExportJobStatus状態表データの正確性を検証
 * テスト方針：
 * 1. DataApplicationServiceの真実インスタンスを使用
 * 2. 核心業務コンポーネント（FileExportOrchestrator、ExportJobStatusService）は真実実行
 * 3. 底層技術コンポーネント（JdbcTemplate、S3Service）のみMockして異常を制造
 * 4. 異常発生後のExportJobStatus状態表データ更新を検証
 */
@ExtendWith(MockitoExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class PlanMasterExportTaskExceptionTest {

    private static final Logger logger = LoggerFactory.getLogger(PlanMasterExportTaskExceptionTest.class);

    // ==================== Mock オブジェクト（底層技術コンポーネントのみ） ====================

    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;

    @Mock
    private S3Service mockS3Service;

    @Mock
    private JdbcTemplate mockJdbcTemplate;

    @Mock
    private Context mockLambdaContext;

    // ==================== テスト対象オブジェクト（真実インスタンス） ====================

    private DataApplicationService dataApplicationService;

    // ==================== Mock 管理 ====================

    private MockedStatic<LambdaResourceManager> mockedLambdaResourceManager;

    @BeforeEach
    void setUp() {
        logger.info("=== 異常テストセットアップ開始 ===");

        try {
            // 基本的なMock設定
            setupBasicMocks();

            // AWS設定を環境変数で設定
            System.setProperty("aws.region", "ap-northeast-1");
            System.setProperty("WORKER_FUNCTION_NAME", "test-worker-function");

            // DataApplicationServiceの真実インスタンスを初期化
            dataApplicationService = new DataApplicationService();

            // 底層技術コンポーネントのみMockに置き換え
            injectMockDependencies();

            logger.info("=== 異常テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("異常テストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("異常テストセットアップに失敗しました", e);
        }
    }

    /**
     * 基本的なMock設定を行う
     * AsyncLambdaInvokerの基本設定（非同期処理をスキップ）
     */
    private void setupBasicMocks() throws Exception {
        lenient().doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any());
        logger.debug("基本的なMock設定が完了しました");
    }

    /**
     * 底層技術コンポーネントのMock依存関係を注入
     * リフレクションを使用してDataApplicationService内の技術コンポーネントのみを置き換え
     */
    private void injectMockDependencies() throws Exception {
        // DataApplicationService内のAsyncLambdaInvokerをMockに置き換え
        Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
        asyncLambdaInvokerField.setAccessible(true);
        asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

        // TaskOrchestrationServiceを取得
        Field taskOrchestrationServiceField = DataApplicationService.class.getDeclaredField("taskOrchestrationService");
        taskOrchestrationServiceField.setAccessible(true);
        Object taskOrchestrationService = taskOrchestrationServiceField.get(dataApplicationService);

        // FileExportOrchestratorを取得
        Field fileExportOrchestratorField = taskOrchestrationService.getClass().getDeclaredField("fileExportOrchestrator");
        fileExportOrchestratorField.setAccessible(true);
        Object fileExportOrchestrator = fileExportOrchestratorField.get(taskOrchestrationService);

        // FileProcessingServiceを取得
        Field fileProcessingServiceField = fileExportOrchestrator.getClass().getDeclaredField("fileProcessingService");
        fileProcessingServiceField.setAccessible(true);
        Object fileProcessingService = fileProcessingServiceField.get(fileExportOrchestrator);

        // FileProcessingService内のS3ServiceをMockに置き換え
        Field s3ServiceField = fileProcessingService.getClass().getDeclaredField("s3Service");
        s3ServiceField.setAccessible(true);
        s3ServiceField.set(fileProcessingService, mockS3Service);

        logger.debug("Mock依存関係の注入が完了しました");
    }

    // ==================== テストケース ====================

    /**
     * データベース異常テスト - SQL実行時例外発生
     * executeExportTask内でSQL実行時にSQLExceptionを制造し、真実の導出フローでの異常処理を検証
     * startExportとgetExportJobStatusFromDatabaseは正常実行、executeExportTaskのみSQL異常を発生させる
     */
    @Test
    @Order(1)
    @DisplayName("データベース異常_SQL実行例外_状態表失敗更新検証")
    void testExportTask_データベース異常_SQL実行例外() {
        logger.info("=== データベース異常テスト開始：SQL実行例外 ===");

        try {
            // テストデータ準備
            ExportRequest exportRequest = createTestExportRequest();
            UserInfo userInfo = createTestUserInfo("1001", "100001");

            // エクスポート処理を開始（ジョブ作成）- この段階では正常に実行
            var exportJobResponse = dataApplicationService.startExport(exportRequest, userInfo, mockLambdaContext);
            Long rrkBango = Long.parseLong(exportJobResponse.getJobId());

            // S3アップロードを正常処理にmock設定（SQL異常テストに集中するため）
            setupS3ServiceForNormalProcessing();

            // executeExportTask実行前にSQL異常mockを設定
            setupSqlExceptionMockForExecuteTask();

            // Worker Lambda処理を直接実行（SQL実行時に異常が発生）
            var payload = createWorkerPayload(exportJobResponse.getJobId(), exportRequest, userInfo);

            // executeExportTask実行（SQL異常は内部で処理され、状態表が更新される）
            assertDoesNotThrow(() -> {
                dataApplicationService.executeExportTask(payload, mockLambdaContext);
            });

            // 状態表データの検証（実際のデータベースから取得）
            ExportJobStatus jobStatus = getExportJobStatusFromDatabase(rrkBango);
            assertNotNull(jobStatus, "ExportJobStatusが取得できませんでした");
            assertEquals(BusinessConstants.BATCH_STATUS_FAILED_CODE, jobStatus.getStts(),
                "ステータスが失敗に更新されていません");
            assertNotNull(jobStatus.getFileSksKnrNchj(), "ファイル作成完了日時が更新されていません");

            logger.info("=== データベース異常テスト完了：SQL実行例外 ===");

        } catch (Exception e) {
            logger.error("データベース異常テストエラー: {}", e.getMessage(), e);
            fail("データベース異常テストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * S3アップロード異常テスト - S3接続エラー発生
     * S3ServiceでIO例外を制造し、真実の導出フローでの異常処理を検証
     * SQL処理は正常実行、S3アップロード時のみ異常を発生させる
     * 完全フロー検証：正常開始から異常終了まで、状態変遷とバージョン更新を含む
     */
    @Test
    @Order(2)
    @DisplayName("S3アップロード異常_完全フロー検証_正常開始から異常終了まで")
    void testExportTask_S3アップロード異常_接続エラー() {
        logger.info("=== S3アップロード異常テスト開始：接続エラー ===");

        try {
            // テストデータ準備
            ExportRequest exportRequest = createTestExportRequest();
            UserInfo userInfo = createTestUserInfo("1002", "100001");

            // S3ServiceでIO例外を制造（アップロード時にエラー）
            when(mockS3Service.uploadFileFromStream(any(), anyString(), anyString(), anyLong(), any()))
                .thenThrow(new IOException("S3接続エラー: ネットワークタイムアウト"));

            // エクスポート処理を開始（ジョブ作成）
            var exportJobResponse = dataApplicationService.startExport(exportRequest, userInfo, mockLambdaContext);
            Long rrkBango = Long.parseLong(exportJobResponse.getJobId());

            // 初期状態の確認（完全フロー検証のため）
            ExportJobStatus initialStatus = getExportJobStatusFromDatabase(rrkBango);
            assertNotNull(initialStatus, "初期ExportJobStatusが取得できませんでした");
            assertEquals(BusinessConstants.BATCH_STATUS_PROCESSING_CODE, initialStatus.getStts(),
                "初期ステータスが処理中になっていません");

            // Worker Lambda処理を直接実行（S3アップロード時に異常が発生）
            var payload = createWorkerPayload(exportJobResponse.getJobId(), exportRequest, userInfo);

            // executeExportTask実行（異常は内部で処理され、状態表が更新される）
            assertDoesNotThrow(() -> {
                dataApplicationService.executeExportTask(payload, mockLambdaContext);
            });

            // 最終状態の検証（完全フロー検証を含む）
            ExportJobStatus finalStatus = getExportJobStatusFromDatabase(rrkBango);
            assertNotNull(finalStatus, "最終ExportJobStatusが取得できませんでした");
            assertEquals(BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE, finalStatus.getStts(),
                "ステータスがシステムエラーに更新されていません");
            assertNotNull(finalStatus.getFileSksKnrNchj(), "ファイル作成完了日時が更新されていません");

            // バージョンが更新されていることを確認（楽観的ロック検証）
            assertTrue(finalStatus.getVrsn() > initialStatus.getVrsn(),
                "バージョンが更新されていません");

            logger.info("=== S3アップロード異常テスト完了：接続エラー ===");

        } catch (Exception e) {
            logger.error("S3アップロード異常テストエラー: {}", e.getMessage(), e);
            fail("S3アップロード異常テストに失敗しました: " + e.getMessage());
        }
    }

    // ==================== ヘルパーメソッド ====================

    /**
     * S3アップロードを正常処理にmock設定
     * SQL異常テストに集中するため、S3関連の処理は正常に動作させる
     */
    private void setupS3ServiceForNormalProcessing() throws Exception {
        // S3アップロードを正常処理として設定（uploadFileFromStreamは Map<String, Object> を返す）
        Map<String, Object> uploadResult = Map.of(
            "success", true,
            "key", "test-s3-key-" + System.currentTimeMillis(),
            "eTag", "test-etag-123"
        );
        when(mockS3Service.uploadFileFromStream(any(), anyString(), anyString(), anyLong(), any()))
            .thenReturn(uploadResult);

        // ダウンロードURL生成を正常処理として設定
        when(mockS3Service.getSignedDownloadUrl(anyString(), anyLong()))
            .thenReturn("https://test-presigned-url.com/test-file");

        logger.debug("S3Service正常処理mock設定完了: アップロードとURL生成が正常に動作します");
    }

    /**
     * executeExportTask専用のSQL実行異常をmockで制造するためのセットアップ
     * PlanMasterDataStrategy.buildQueryをmockして無効なSQLを返すことでSQLExceptionを発生させる
     * この方法により、SQL生成段階で異常を制造し、他のデータベース操作には影響しない
     */
    private void setupSqlExceptionMockForExecuteTask() throws Exception {
        // PlanMasterExportService内のPlanMasterDataStrategyをmockに置き換える
        PlanMasterExportService planMasterExportService = getPlanMasterExportServiceFromDataApplicationService();

        // PlanMasterDataStrategyのmockを作成
        PlanMasterDataStrategy mockPlanMasterDataStrategy = mock(PlanMasterDataStrategy.class);

        // buildQueryメソッドをmockして無効なSQLを返す
        when(mockPlanMasterDataStrategy.buildQuery(any(), any())).thenAnswer(invocation -> {
            // 無効なSQL文を含むSqlWithParamsを返す（存在しないテーブル名を使用）
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("param0", "2025");

            // 存在しないテーブル名を使用して確実にSQLExceptionを発生させる
            String invalidSql = "SELECT * FROM NON_EXISTENT_TABLE_FOR_TEST WHERE invalid_column = ?";

            return new DataAccessStrategy.SqlWithParams(invalidSql, paramMap);
        });

        lenient().when(mockPlanMasterDataStrategy.getStrategyName()).thenReturn("PLAN_MASTER_DEFAULT");

        // リフレクションを使用してPlanMasterExportService内のstrategyを置き換える
        replacePlanMasterDataStrategyInExportService(planMasterExportService, mockPlanMasterDataStrategy);

        logger.debug("executeExportTask専用SQL実行異常mock設定完了: PlanMasterDataStrategy.buildQueryで無効なSQLを返します");
    }


    /**
     * テスト用ユーザー情報を作成
     * 正常なテストケース用の有効なユーザー情報を生成
     */
    private UserInfo createTestUserInfo(String shainCode, String systemOperationCompanyCode) {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode(shainCode);
        userInfo.setSystemOperationCompanyCode(systemOperationCompanyCode);
        return userInfo;
    }

    /**
     * テスト用エクスポートリクエストを作成
     * 次年度計画マスタエクスポート用の標準的なリクエストオブジェクトを生成
     */
    private ExportRequest createTestExportRequest() {
        ExportRequest exportRequest = new ExportRequest();
        exportRequest.setDataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);
        exportRequest.setArea(Arrays.asList("9002", "9003"));
        exportRequest.setHnshBashoKubun("1"); // 本社
        exportRequest.setDataKubun(Arrays.asList("1")); // 計画
        exportRequest.setCtgryKubun("1"); // カテゴリ区分
        return exportRequest;
    }

    /**
     * Worker Lambda用ペイロードを作成
     * DataApplicationService.executeExportTask用のペイロードオブジェクトを生成
     */
    private WorkerPayload createWorkerPayload(String jobId, ExportRequest exportRequest, UserInfo userInfo) {
        return WorkerPayload.builder()
                .jobId(jobId)
                .operationType(BusinessConstants.OPERATION_DOWNLOAD_CODE)
                .request(exportRequest)
                .userInfo(userInfo)
                .build();
    }

    /**
     * データベースからExportJobStatusを取得
     * 状態表データの検証用
     */
    private ExportJobStatus getExportJobStatusFromDatabase(Long rrkBango) {
        try {
            // 真実のExportJobStatusServiceインスタンスを使用してデータベースから取得
            ExportJobStatusService exportJobStatusService = new ExportJobStatusService();
            return exportJobStatusService.getJobStatus(rrkBango);
        } catch (Exception e) {
            logger.error("ExportJobStatus取得エラー: rrkBango={}, error={}", rrkBango, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Mock設定をリセット
     * テスト間でMockの状態をクリーンアップ
     */
    private void resetMocks() {
        reset(mockAsyncLambdaInvoker, mockS3Service, mockJdbcTemplate, mockLambdaContext);

        // 基本的なMock設定を再適用
        try {
            setupBasicMocks();
        } catch (Exception e) {
            logger.error("Mock設定の再適用中にエラーが発生しました: {}", e.getMessage(), e);
        }
    }


    @AfterEach
    void tearDown() {
        logger.info("=== 異常テストクリーンアップ開始 ===");

        try {
            // 静的Mockをクローズ
            if (mockedLambdaResourceManager != null) {
                mockedLambdaResourceManager.close();
                mockedLambdaResourceManager = null;
            }

            // Mock状態をリセット
            resetMocks();

        } catch (Exception e) {
            logger.error("テストクリーンアップ中にエラーが発生しました: {}", e.getMessage(), e);
        }

        logger.info("=== 異常テストクリーンアップ完了 ===");
    }

    // ==================== リフレクション用ヘルパーメソッド ====================

    /**
     * DataApplicationServiceからPlanMasterExportServiceを取得
     * リフレクションを使用してprivateフィールドにアクセス
     */
    private PlanMasterExportService getPlanMasterExportServiceFromDataApplicationService() throws Exception {
        // DataApplicationService内のTaskOrchestrationServiceを取得
        Field taskOrchestrationServiceField = dataApplicationService.getClass().getDeclaredField("taskOrchestrationService");
        taskOrchestrationServiceField.setAccessible(true);
        Object taskOrchestrationService = taskOrchestrationServiceField.get(dataApplicationService);

        // TaskOrchestrationService内のFileExportOrchestratorを取得
        Field fileExportOrchestratorField = taskOrchestrationService.getClass().getDeclaredField("fileExportOrchestrator");
        fileExportOrchestratorField.setAccessible(true);
        Object fileExportOrchestrator = fileExportOrchestratorField.get(taskOrchestrationService);

        // FileExportOrchestrator内のexportServicesマップを取得
        Field exportServicesField = fileExportOrchestrator.getClass().getDeclaredField("exportServices");
        exportServicesField.setAccessible(true);
        @SuppressWarnings("unchecked")
        Map<String, Object> exportServices = (Map<String, Object>) exportServicesField.get(fileExportOrchestrator);

        // PLAN_MASTER_CODEキーでPlanMasterExportServiceを取得
        return (PlanMasterExportService) exportServices.get(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);
    }

    /**
     * PlanMasterExportService内のPlanMasterDataStrategyを置き換える
     * リフレクションを使用してsqlStrategyManager内のstrategyを置き換える
     */
    private void replacePlanMasterDataStrategyInExportService(PlanMasterExportService exportService, PlanMasterDataStrategy mockStrategy) throws Exception {
        // AbstractExportService内のsqlStrategyManagerを取得
        Field sqlStrategyManagerField = exportService.getClass().getSuperclass().getDeclaredField("sqlStrategyManager");
        sqlStrategyManagerField.setAccessible(true);
        Object sqlStrategyManager = sqlStrategyManagerField.get(exportService);

        // SqlStrategyManager内のstrategiesマップを取得
        Field strategiesField = sqlStrategyManager.getClass().getDeclaredField("strategies");
        strategiesField.setAccessible(true);
        @SuppressWarnings("unchecked")
        Map<String, DataAccessStrategy> strategies = (Map<String, DataAccessStrategy>) strategiesField.get(sqlStrategyManager);

        // PLAN_MASTER_DEFAULTキーでmockStrategyを登録
        strategies.put("PLAN_MASTER_DEFAULT", mockStrategy);

        logger.debug("PlanMasterDataStrategyをmockに置き換えました");
    }

}
