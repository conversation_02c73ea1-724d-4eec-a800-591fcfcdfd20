# ローカル開発・デバッグガイド

## 概要

このドキュメントでは、最新のDDD（ドメイン駆動設計）アーキテクチャに基づいたプロジェクトのローカル開発環境の構築とデバッグ方法について説明します。2025年6月のアーキテクチャリファクタリング完了により、適切な層分離と責任分担が実現されています。

## DDD アーキテクチャでの開発アプローチ

本プロジェクトは最新のDDD（Domain-Driven Design）原則に基づいて設計されており、以下の層構造を持ちます：

- **インターフェース層**: HTTP処理のみに責任を限定（DataController: 224行、PermissionController: 104行）
- **アプリケーション層**: ビジネスフロー協調とタスク管理（DataApplicationService: 295行、TaskOrchestrationService: 183行）
- **ドメイン層**: ビジネスロジックとビジネスルール（権限管理ドメイン追加）
- **インフラストラクチャ層**: 技術的実装の専門処理（FileProcessingService: 286行、AsyncTaskExecutor: 56行）
- **共有層**: セキュリティコンポーネントの共有化（ApiAuthMiddleware、JwtTokenGenerator、PasswordEncoder）

## 前提条件

### 必要なソフトウェア

- **Java 21** - OpenJDK 21以上
- **Maven 3.8+** - ビルドツール
- **Git** - バージョン管理
- **AWS SAM CLI** - AWS Serverless Application Model (SAM) CLI
- **AWS CLI** - AWS Command Line Interface (CLI)
- **IDE** - IntelliJ IDEA
- **Plugin** - Aws Toolkit
- **Docker**
- **PostgreSQL 13+** - データベース（オプション）
- **Bitvise SSH Client** - SSH隧道管理（オプション）

## 環境セットアップ

### 1. プロジェクトのクローン

```bash
git clone <repository-url>
```

### 2. PostgreSQLデータベースのセットアップ（オプション）

#### データベース作成
```sql
-- PostgreSQLに接続してデータベースを作成
```

#### テーブル作成
```bash
# データベーススキーマの実行
```

### 3. 設定ファイルの準備

#### application.properties の設定

**ローカルデータベース使用の場合:**
```properties
# Database configuration (ローカル開発用)
db.use.parameter.store=false
db.host=localhost
db.port=5432
db.name=business_plan_db
db.username=bp_user
db.password=bp_password

# SSH隧道設定（無効）
ssh.tunnel.enabled=false

# AWS設定（ローカル開発では無効）
aws.region=ap-northeast-1

# JWT設定
jwt.secret.key=your-local-development-secret-key-change-in-production

# ログ設定
aws.lambda.log.format=TEXT
aws.lambda.log.level=DEBUG

# メール設定（ローカル開発では無効）
email.default.from=noreply@localhost

# フロントエンド設定
frontend.base.url=http://localhost:3000
```

**AWS dev データベースにSSH隧道経由で接続する場合:**
```properties
# Database configuration (AWS dev データベース用)
db.use.parameter.store=true
db.parameter.prefix=/ms-bp/dev/standard/db

# SSH隧道設定（有効）
ssh.tunnel.enabled=true
ssh.tunnel.ssh.host=your-bastion-host.amazonaws.com
ssh.tunnel.ssh.port=22
ssh.tunnel.ssh.username=ec2-user
ssh.tunnel.ssh.private.key.path=~/.ssh/your-aws-key.pem
ssh.tunnel.local.port=15432
ssh.tunnel.remote.host=your-aurora-cluster.cluster-xxxxx.ap-northeast-1.rds.amazonaws.com
ssh.tunnel.remote.port=5432
ssh.tunnel.connection.timeout=30000
ssh.tunnel.keep.alive.interval=60000

# AWS設定
aws.region=ap-northeast-1

# JWT設定
jwt.secret.key=your-local-development-secret-key-change-in-production

# ログ設定
aws.lambda.log.format=TEXT
aws.lambda.log.level=DEBUG

# メール設定（ローカル開発では無効）
email.default.from=noreply@localhost

# フロントエンド設定
frontend.base.url=http://localhost:3000
```

### 4. SSH隧道設定（AWS dev データベース接続用）

#### 前提条件
- AWS EC2 Bastion Host（踏み台サーバー）へのアクセス権限
- SSH秘密鍵ファイル（.pemファイル）
- Aurora PostgreSQLクラスターのエンドポイント情報


### 5. SSH隧道管理方法別資料を参照
- Bitvise SSH Client


## 開発環境での実行

### 1. IDEでの実行

#### IntelliJ IDEAの設定
### デバッグオプションの設定
* **Event**: デバッグ時にLambda関数に送信される入力イベントです。以下のオプションが利用できます。
    * JSON形式のイベントペイロードを手動で編集します。

* **Configuration**:
    * **Runtime**: ランタイム環境を確認します。
    * **Handler**: 関数ハンドラの場所を確認します。
    * **Environment Variables**: 必要に応じて、ローカルデバッグセッション用の環境変数を設定します。

* **SAM CLI**:
    * **Build function inside a container**: この項目にチェックを入れることを強く推奨します。これにより、Lambdaの生産環境とより一貫性のある環境で関数がビルド・実行され、ローカル環境との差異に起因する問題を回避できます。
    * **Docker Network**: 関数がローカルネットワーク上の他のコンテナ（例：ローカルデータベース）にアクセスする必要がある場合、ここでDockerネットワークを指定できます。

### 4. ログレベル設定

#### application.properties でのログ設定
```properties
# 全体的なデバッグレベル
aws.lambda.log.level=DEBUG

# DDD層別ログレベル設定
logging.level.com.ms.bp.domain=DEBUG
logging.level.com.ms.bp.application=DEBUG
logging.level.com.ms.bp.infrastructure=DEBUG
logging.level.com.ms.bp.interfaces=INFO

# 特定サービスの詳細ログ
logging.level.com.ms.bp.domain.user.UserAuthenticationService=TRACE
logging.level.com.ms.bp.domain.file.user.UserImportService=TRACE

# データアクセスログ
logging.level.com.ms.bp.infrastructure.repository=DEBUG
logging.level.org.springframework.jdbc.core.JdbcTemplate=DEBUG
```