<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Plan Lambda Application - DDD依存関係図</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap');
        body { font-family: 'Noto Sans JP', sans-serif; }
        
        .layer-box {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .layer-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        
        .arrow {
            stroke: #4b5563;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }
        
        .arrow-dashed {
            stroke-dasharray: 5, 5;
        }
        
        .interface-impl-arrow {
            stroke: #7c3aed;
            stroke-width: 2.5;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- ヘッダー -->
        <header class="text-center mb-12">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">
                Business Plan Lambda Application
            </h1>
            <p class="text-xl text-gray-600">DDD 4層アーキテクチャの依存関係</p>
        </header>

        <!-- メイン図 -->
        <section class="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <h2 class="text-2xl font-bold text-center mb-8">アーキテクチャ全体図</h2>
            
            <!-- SVG図 -->
            <div class="flex justify-center mb-8">
                <svg width="800" height="600" viewBox="0 0 800 600">
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#4b5563" />
                        </marker>
                        <marker id="arrowhead-purple" markerWidth="10" markerHeight="7" 
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#7c3aed" />
                        </marker>
                    </defs>
                    
                    <!-- Interface層 -->
                    <g id="interface-layer">
                        <rect x="50" y="50" width="700" height="100" rx="8" 
                              fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
                        <text x="400" y="85" text-anchor="middle" 
                              font-size="20" font-weight="bold" fill="#1e40af">
                            Interface層（インターフェース層）
                        </text>
                        <text x="400" y="110" text-anchor="middle" 
                              font-size="14" fill="#1e40af">
                            LambdaHandler, DataController, FileController, PermissionController
                        </text>
                        <text x="400" y="130" text-anchor="middle" 
                              font-size="12" fill="#64748b">
                            責務：HTTPリクエスト/レスポンス処理、入力検証、エラーハンドリング
                        </text>
                    </g>
                    
                    <!-- Application層 -->
                    <g id="application-layer">
                        <rect x="50" y="200" width="700" height="100" rx="8" 
                              fill="#d1fae5" stroke="#10b981" stroke-width="2"/>
                        <text x="400" y="235" text-anchor="middle" 
                              font-size="20" font-weight="bold" fill="#047857">
                            Application層（アプリケーション層）
                        </text>
                        <text x="400" y="260" text-anchor="middle" 
                              font-size="14" fill="#047857">
                            DataApplicationService, TaskOrchestrationService, FileSplitStrategyManager
                        </text>
                        <text x="400" y="280" text-anchor="middle" 
                              font-size="12" fill="#64748b">
                            責務：ユースケース実現、トランザクション管理、ドメインサービス調整
                        </text>
                    </g>
                    
                    <!-- Domain層 -->
                    <g id="domain-layer">
                        <rect x="50" y="350" width="340" height="180" rx="8" 
                              fill="#e9d5ff" stroke="#9333ea" stroke-width="3"/>
                        <text x="220" y="385" text-anchor="middle" 
                              font-size="20" font-weight="bold" fill="#6b21a8">
                            Domain層（ドメイン層）
                        </text>
                        <text x="220" y="410" text-anchor="middle" 
                              font-size="12" fill="#6b21a8">
                            UserService, PermissionService
                        </text>
                        <text x="220" y="430" text-anchor="middle" 
                              font-size="12" fill="#6b21a8">
                            User, Permission (Entity)
                        </text>
                        <text x="220" y="450" text-anchor="middle" 
                              font-size="12" fill="#6b21a8">
                            UserRepository (Interface)
                        </text>
                        <text x="220" y="470" text-anchor="middle" 
                              font-size="12" fill="#6b21a8">
                            AbstractImportService
                        </text>
                        <text x="220" y="495" text-anchor="middle" 
                              font-size="11" fill="#64748b">
                            責務：ビジネスロジック実装
                        </text>
                        <text x="220" y="510" text-anchor="middle" 
                              font-size="11" fill="#64748b">
                            ビジネスルール、状態管理
                        </text>
                    </g>
                    
                    <!-- Infrastructure層 -->
                    <g id="infrastructure-layer">
                        <rect x="410" y="350" width="340" height="180" rx="8" 
                              fill="#fed7aa" stroke="#ea580c" stroke-width="2"/>
                        <text x="580" y="385" text-anchor="middle" 
                              font-size="20" font-weight="bold" fill="#9a3412">
                            Infrastructure層
                        </text>
                        <text x="580" y="410" text-anchor="middle" 
                              font-size="12" fill="#9a3412">
                            UserRepositoryImpl
                        </text>
                        <text x="580" y="430" text-anchor="middle" 
                              font-size="12" fill="#9a3412">
                            S3Service, EmailService
                        </text>
                        <text x="580" y="450" text-anchor="middle" 
                              font-size="12" fill="#9a3412">
                            AuroraConnectionManager
                        </text>
                        <text x="580" y="470" text-anchor="middle" 
                              font-size="12" fill="#9a3412">
                            AsyncTaskExecutor
                        </text>
                        <text x="580" y="495" text-anchor="middle" 
                              font-size="11" fill="#64748b">
                            責務：技術的実装
                        </text>
                        <text x="580" y="510" text-anchor="middle" 
                              font-size="11" fill="#64748b">
                            DB接続、外部API連携
                        </text>
                    </g>
                    
                    <!-- 依存関係の矢印 -->
                    <!-- Interface → Application -->
                    <path d="M 400 150 L 400 200" class="arrow"/>
                    
                    <!-- Application → Domain -->
                    <path d="M 350 300 L 300 350" class="arrow"/>
                    
                    <!-- Application → Infrastructure -->
                    <path d="M 450 300 L 500 350" class="arrow"/>
                    
                    <!-- Infrastructure → Domain (Interface実装) -->
                    <path d="M 410 440 L 390 440" 
                          class="arrow interface-impl-arrow" 
                          stroke-dasharray="5, 5"
                          marker-end="url(#arrowhead-purple)"/>
                    <text x="400" y="435" text-anchor="middle" 
                          font-size="11" fill="#7c3aed" font-weight="bold">
                        implements
                    </text>
                    
                    <!-- 凡例 -->
                    <g id="legend" transform="translate(50, 550)">
                        <text x="0" y="0" font-size="14" font-weight="bold" fill="#374151">凡例：</text>
                        <line x1="80" y1="-5" x2="120" y2="-5" class="arrow" marker-end="url(#arrowhead)"/>
                        <text x="130" y="0" font-size="12" fill="#374151">依存関係</text>
                        <line x1="250" y1="-5" x2="290" y2="-5" 
                              class="arrow interface-impl-arrow arrow-dashed" 
                              marker-end="url(#arrowhead-purple)"/>
                        <text x="300" y="0" font-size="12" fill="#374151">インターフェース実装</text>
                    </g>
                </svg>
            </div>

            <!-- 依存関係の説明 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                <div class="bg-blue-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-3 text-blue-800">✅ 正しい依存の方向</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-start">
                            <span class="text-blue-600 mr-2">→</span>
                            <span><strong>Interface層</strong> は Application層 に依存</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-blue-600 mr-2">→</span>
                            <span><strong>Application層</strong> は Domain層とInfrastructure層 に依存</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-blue-600 mr-2">→</span>
                            <span><strong>Infrastructure層</strong> は Domain層のインターフェース を実装</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-blue-600 mr-2">⭐</span>
                            <span><strong>Domain層</strong> は他の層に依存しない（独立）</span>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-purple-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-3 text-purple-800">🎯 依存関係逆転の原則（DIP）</h3>
                    <p class="text-sm mb-3">
                        Domain層で定義したRepositoryインターフェースを、Infrastructure層で実装することで実現
                    </p>
                    <div class="bg-white rounded p-3 text-sm">
                        <p class="font-medium mb-1">例：UserRepository</p>
                        <ul class="space-y-1 text-xs">
                            <li>• Domain層：UserRepository（インターフェース）</li>
                            <li>• Infrastructure層：UserRepositoryImpl（実装）</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 本プロジェクトの実践例 -->
        <section class="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <h2 class="text-2xl font-bold text-center mb-8">本プロジェクトでの実践例</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- ユーザー管理機能の例 -->
                <div class="border-2 border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">👤 ユーザー管理機能</h3>
                    <div class="space-y-4">
                        <div class="layer-box bg-blue-50 rounded p-3">
                            <p class="font-medium text-blue-700 mb-1">Interface層</p>
                            <p class="text-sm">UserAuthController</p>
                            <p class="text-xs text-gray-600">ログインリクエスト受付</p>
                        </div>
                        <div class="text-center text-gray-400">↓</div>
                        <div class="layer-box bg-green-50 rounded p-3">
                            <p class="font-medium text-green-700 mb-1">Application層</p>
                            <p class="text-sm">UserAuthApplicationService</p>
                            <p class="text-xs text-gray-600">認証フロー調整、メール送信</p>
                        </div>
                        <div class="text-center text-gray-400">↓</div>
                        <div class="layer-box bg-purple-50 rounded p-3">
                            <p class="font-medium text-purple-700 mb-1">Domain層</p>
                            <p class="text-sm">UserAuthenticationService</p>
                            <p class="text-xs text-gray-600">パスワード検証、状態チェック</p>
                        </div>
                    </div>
                </div>

                <!-- ファイルエクスポート機能の例 -->
                <div class="border-2 border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">📁 ファイルエクスポート機能</h3>
                    <div class="space-y-4">
                        <div class="layer-box bg-blue-50 rounded p-3">
                            <p class="font-medium text-blue-700 mb-1">Interface層</p>
                            <p class="text-sm">DataController</p>
                            <p class="text-xs text-gray-600">エクスポートAPI</p>
                        </div>
                        <div class="text-center text-gray-400">↓</div>
                        <div class="layer-box bg-green-50 rounded p-3">
                            <p class="font-medium text-green-700 mb-1">Application層</p>
                            <p class="text-sm">DataApplicationService</p>
                            <p class="text-sm">TaskOrchestrationService</p>
                            <p class="text-xs text-gray-600">非同期処理、戦略選択</p>
                        </div>
                        <div class="flex justify-center text-gray-400">
                            <span class="mr-8">↙</span>
                            <span class="ml-8">↘</span>
                        </div>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="layer-box bg-purple-50 rounded p-3">
                                <p class="font-medium text-purple-700 mb-1 text-sm">Domain層</p>
                                <p class="text-xs">UserExportService</p>
                                <p class="text-xs text-gray-600">データ変換</p>
                            </div>
                            <div class="layer-box bg-orange-50 rounded p-3">
                                <p class="font-medium text-orange-700 mb-1 text-sm">Infra層</p>
                                <p class="text-xs">S3Service</p>
                                <p class="text-xs text-gray-600">ファイル保存</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 重要なポイント -->
        <section class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-8">
            <h2 class="text-2xl font-bold text-center mb-8 text-indigo-800">🎓 アーキテクチャの重要ポイント</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg p-6 shadow-md">
                    <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                        <span class="text-2xl">🎯</span>
                    </div>
                    <h3 class="font-semibold mb-2">Domain層の独立性</h3>
                    <p class="text-sm text-gray-600">
                        ビジネスロジックは技術的な詳細から完全に独立。
                        フレームワークやDBの変更に影響されない。
                    </p>
                </div>
                
                <div class="bg-white rounded-lg p-6 shadow-md">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                        <span class="text-2xl">🔄</span>
                    </div>
                    <h3 class="font-semibold mb-2">依存関係逆転の原則</h3>
                    <p class="text-sm text-gray-600">
                        Repositoryパターンで実現。Domain層でインターフェースを定義し、
                        Infrastructure層で実装。
                    </p>
                </div>
                
                <div class="bg-white rounded-lg p-6 shadow-md">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                        <span class="text-2xl">🧪</span>
                    </div>
                    <h3 class="font-semibold mb-2">高いテスタビリティ</h3>
                    <p class="text-sm text-gray-600">
                        各層が独立しているため、モックを使った単体テストが容易。
                        ビジネスロジックを独立してテスト可能。
                    </p>
                </div>
            </div>
            
            <div class="mt-8 text-center">
                <p class="text-lg font-medium text-indigo-700">
                    このアーキテクチャにより、<br>
                    <span class="text-xl font-bold">変更に強く、テストしやすく、理解しやすい</span><br>
                    コードベースを実現しています。
                </p>
            </div>
        </section>
    </div>
</body>
</html>