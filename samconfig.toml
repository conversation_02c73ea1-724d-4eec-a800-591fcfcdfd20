version = 0.1

[default.deploy.parameters]
region = "ap-northeast-1"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
stack_name = "ms-bp-devtest-lambda"
resolve_s3 = true
s3_prefix = "msbp"
image_repositories = []

[dev.deploy.parameters]
region = "ap-northeast-1"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
stack_name = "ms-bp-devstandard-lambda"
resolve_s3 = true
s3_prefix = "msbp"
image_repositories = []

[stg.deploy.parameters]
region = "ap-northeast-1"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
stack_name = "ms-bp-stgstandard-lambda"
resolve_s3 = true
s3_prefix = "msbp"
image_repositories = []

[prod.deploy.parameters]
region = "ap-northeast-1"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
stack_name = "ms-bp-prodstandard-lambda"
resolve_s3 = true
s3_prefix = "msbp"
image_repositories = []
