# Business Plan Lambda Application

## 概要

このプロジェクトは、ユーザー認証、データインポート/エクスポート、ファイル管理、権限管理などの機能を提供するAWS Lambda ベースのサーバーレスアプリケーションです。
DDD（ドメイン駆動設計）原則に基づいて設計・実装されています。

## 主要機能

- **ユーザー認証**: JWT トークンベースの認証システム（アクティベーション・パスワードリセット対応）
- **データインポート/エクスポート**: CSV ファイルの一括処理（完全非同期処理対応）
- **ファイル管理**: S3 を使用したファイルアップロード/ダウンロード（ZIP圧縮対応）
- **権限管理**: 細かい粒度での権限制御（アップロード/ダウンロード別、SQL最適化済み）
- **ファイル分割戦略**: エリア別、部門別、件数別、複合条件などの柔軟な分割処理（Strategy Pattern）
- **非同期処理**: バックグラウンドでの大容量データ処理（ジョブ管理・進捗追跡対応）
- **エラーハンドリング**: 統一されたエラー処理とログ記録（エラーファイル生成対応）
- **設定管理**: AWS Parameter Store を使用した環境別設定管理

## 技術スタック

### コア技術
- **Java 21**: プログラミング言語（OpenJDK 21）
- **AWS Lambda**: サーバーレス実行環境（Java 21 Runtime）
- **Maven 3.8+**: ビルドツール・依存関係管理

### AWS サービス
- **Amazon Aurora PostgreSQL**: データベース（AWS JDBC Wrapper使用）
- **Amazon S3**: ファイルストレージ（アップロード/ダウンロード）
- **Amazon SES**: メール送信サービス
- **AWS Parameter Store**: 環境別設定管理
- **AWS SAM CLI**: デプロイメント・ローカル開発

### ライブラリ・フレームワーク
- **JWT (Auth0)**: 認証トークン（v4.4.0）
- **Jackson**: JSON処理（v2.15.3）
- **OpenCSV**: CSV処理（v5.8）
- **HikariCP**: データベース接続プール（v5.0.1）
- **PostgreSQL JDBC**: データベースドライバ（v42.7.2）
- **Lombok**: コード生成（v1.18.30）
- **SLF4J**: ログ処理（v2.0.7）
- **JUnit 5**: テストフレームワーク
- **Mockito**: モックテスト（v5.8.0）


## DDD設計原則

### 層間依存関係
```
インターフェース層 → アプリケーション層 → ドメイン層 ← インフラストラクチャ層
```

### 主要パターン
- **Repository パターン**: データアクセスの抽象化（repository.impl パッケージ）
- **Factory パターン**: ドメインサービスの生成管理（DomainServiceFactory）
- **Template Method パターン**: 共通処理フローの定義（AbstractImportService/AbstractExportService）
- **Strategy パターン**: ファイル分割戦略の実装（FileSplitStrategy + 5種類の実装）
- **Domain Model パターン**: リッチドメインモデルの実装（ビジネスメソッド付きエンティティ）
- **Orchestrator パターン**: 複雑なビジネスフローの協調（FileExportOrchestrator/FileImportOrchestrator）
- **DTO Mapper パターン**: DatabaseMappable による高性能データ変換（反射回避）

### アーキテクチャ特徴
- **完全非同期処理**: インポート/エクスポート処理の完全バックグラウンド実行
- **ジョブ管理システム**: 進捗追跡・エラーハンドリング
- **戦略的ファイル分割**: ビジネス要件に応じた柔軟なファイル分割
- **セキュリティ統合**: JWT認証・権限チェック・SSH隧道接続

## 主要API

### 認証関連（UserAuthController）TODO


### データ管理（DataController）
- `POST /api/data/import` - データインポート（非同期処理、即座にジョブID返却）
- `POST /api/data/export` - データエクスポート（非同期処理、戦略的ファイル分割）
- `GET /api/data/import/history` - インポート履歴取得
- `GET /api/data/export/history` - エクスポート履歴取得

### 権限管理（PermissionController）
- `GET /api/permissions/user` - ユーザー権限一覧取得（ページ初期化用）
- `POST /api/permissions/check` - 権限チェック（アップロード/ダウンロード別、ファイルタイプ別）

### ファイル管理（FileController）
- `POST /api/files/uploadUrl` - アップロードURL生成エンドポイント
- `POST /api/files/downloadUrl` - ファイルダウンロード（署名付きURL生成）

## 設定管理

### 設定ファイル構成
- `application.properties` - デフォルト設定（開発環境、SSH隧道対応）
- `application-uat.properties` - UAT環境設定（Parameter Store使用）
- `application-prod.properties` - 本番環境設定（Parameter Store使用）

### データベース接続設定

接続情報の取得優先順位：
1. **AWS Systems Manager Parameter Store**（UAT・本番環境）
2. **設定ファイル**（ローカル開発環境）
3. **SSH隧道接続**（開発環境でのAWSデータベース接続）

#### Parameter Store設定

各環境に以下のパラメータを設定：

| パラメータ名 | タイプ | 説明 |
|-------------|-------|------|
| `/ms-bp/{環境}/standard/db/host` | String | データベースホスト名 |
| `/ms-bp/{環境}/standard/db/DataBaseName` | String | データベース名 |
| `/ms-bp/{環境}/standard/db/MasterUsername` | String | マスターユーザー名 |
| `/ms-bp/{環境}/standard/db/MasterUserPassword` | SecureString | マスターユーザーパスワード |
| `/ms-bp/{環境}/standard/db/port` | String | ポート番号（オプション） |

#### SSH隧道設定（開発環境）

```properties
# SSH隧道設定
ssh.tunnel.enabled=true
ssh.tunnel.ssh.host=*************
ssh.tunnel.ssh.port=22
ssh.tunnel.ssh.username=ec2-user
ssh.tunnel.ssh.private.key.path=src/main/resources/ms-bp-key-ec2-dev.pem
ssh.tunnel.local.port=15432
ssh.tunnel.remote.host=ms-bp-db-dev-standard.cra8u4cw216c.ap-northeast-1.rds.amazonaws.com
ssh.tunnel.remote.port=5432
```


## ビルド・デプロイメント

### ローカルビルド
```bash
# 依存関係のインストールとコンパイル
mvn clean compile

# テスト実行
mvn test

# パッケージ作成（Lambda用JAR）
mvn clean package
```
## 主要な処理フロー

### インポート処理フロー
1. **DataController** - HTTP リクエスト受信・権限チェック
2. **DataApplicationService** - ビジネス協調・ジョブ作成
3. **TaskOrchestrationService** - 非同期タスク協調
4. **FileImportOrchestrator** - インポート処理協調
5. **AbstractImportService** - ドメインロジック実行
6. **ImportTemplate** - バッチ処理・エラーハンドリング

### エクスポート処理フロー
1. **DataController** - HTTP リクエスト受信・権限チェック
2. **DataApplicationService** - ビジネス協調・ジョブ作成
3. **TaskOrchestrationService** - 非同期タスク協調
4. **FileExportOrchestrator** - エクスポート処理協調
5. **FileSplitStrategy** - ファイル分割戦略決定
6. **AbstractExportService** - ドメインロジック実行
7. **ZipFileService** - ZIP圧縮・S3アップロード

## ドキュメント

- [DDD設計](docs/README-DDD.md) - ドメイン駆動設計
- [開発ガイド](docs/DEVELOPMENT-GUIDE.md) - 開発ガイド - インポート・エクスポート機能
- [設定項目詳細](docs/CONFIG-ITEMS-JP.md) - 全設定項目の説明
- [ローカル開発・デバッグガイド](docs/LOCAL-DEVELOPMENT-DEBUG.md) - ローカル環境での開発とデバッグ方法

