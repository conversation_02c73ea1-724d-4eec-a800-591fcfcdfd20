version: 0.2
env:
  variables:
    PROJECT_PREFIX: "ms-bp"
    SUB_PREFIX: "standard"

phases:
  install:
    runtime-versions:
      java: corretto21
    commands:
      - echo "Installing dependencies..."

  pre_build:
    commands:
      - |
        echo "========== START PRE_BUILD =========="
        if [ -z "$CODEBUILD_SOURCE_VERSION" ]; then
          echo "ERROR: CODEBUILD_SOURCE_VERSION is empty"
          exit 1
        fi

        ARN="$CODEBUILD_SOURCE_VERSION"
        if [[ "$ARN" != arn:aws:s3:::* ]]; then
          echo "ERROR: Invalid ARN format: $ARN"
          exit 1
        fi
        
        FULL_PATH="${ARN#arn:aws:s3:::}"
        BUCKET="${FULL_PATH%%/*}"
        KEY="${FULL_PATH#*/}"
        S3_URI="s3://$BUCKET/$KEY"

        echo "Downloading source from: $S3_URI"
        if ! aws s3 cp "$S3_URI" source.zip; then
          echo "FAILED: Unable to download source artifact"
          echo "Available artifacts in bucket:"
          aws s3 ls "s3://$BUCKET/${KEY%/*}/" || true
          exit 1
        fi

        echo "Unzipping source..."
        unzip -q ./source.zip -d ./source || {
          echo "FAILED: Unzip failed";
          exit 1;
        }
        rm -f ./source.zip
        cd ./source
        echo "Current directory contents:"
        ls -la
  build:
    commands:
      - |
        echo "========== START BUILD =========="
        echo "Running SAM build..."
        sam build \
          --template template.${ENV}.yaml \
          --parameter-overrides \
            ProjectPrefix=${PROJECT_PREFIX} \
            Env=${ENV} \
            SubPrefix=${SUB_PREFIX} || {
          echo "FAILED: SAM build failed";
          exit 1;
        }
        echo "========== END BUILD =========="

  post_build:
    commands:
      - |
        echo "========== START POST_BUILD =========="
        echo "Deploying with SAM..."
        sam deploy \
          --config-file samconfig.toml \
          --config-env ${ENV} \
          --parameter-overrides \
            ProjectPrefix=${PROJECT_PREFIX} \
            Env=${ENV} \
            SubPrefix=${SUB_PREFIX} \
          --no-fail-on-empty-changeset || {
          echo "FAILED: SAM deploy failed";
          exit 1;
          }

        echo "========== END POST_BUILD =========="