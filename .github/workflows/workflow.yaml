name: Build and Deploy

on:
  push:
    branches:
      - 'feature/v*'
      - 'main'
    tags:
      - '*'
  pull_request:
    branches:
      - 'feature/v**'  # feature/V で始まるブランチの PR に対してのみトリガーされる

env:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"

jobs:
  build-test-deploy:
    runs-on: [self-hosted]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          java-version: '21'
          distribution: 'corretto'

      - name: Verify Java version
        run: |
          echo "--- Java Version ---"
          java -version
          echo "--- Maven Version ---"
          mvn --version

      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Build with Maven
        run: mvn clean package

      - name: Determine AWS Account
        if: github.event_name == 'push'
        id: aws-account
        run: |
          if [[ $GITHUB_REF == refs/heads/feature/v* ]]; then
            echo "ACCOUNT=************" >> $GITHUB_OUTPUT
            echo "S3_BUCKET=ms-bp-codebuild-dev-standard" >> $GITHUB_OUTPUT
            echo "PIPELINE_NAME=ms-bp-pipeline-dev-standard" >> $GITHUB_OUTPUT
          elif [[ $GITHUB_REF == refs/heads/main ]]; then
            echo "ACCOUNT=*************" >> $GITHUB_OUTPUT
            echo "S3_BUCKET=ms-bp-codebuild-stg-standard" >> $GITHUB_OUTPUT
            echo "PIPELINE_NAME=ms-bp-pipeline-stg-standard" >> $GITHUB_OUTPUT
          elif [[ $GITHUB_REF == refs/tags/* ]]; then
            echo "ACCOUNT=************" >> $GITHUB_OUTPUT
            echo "S3_BUCKET=ms-bp-codebuild-prod-standard" >> $GITHUB_OUTPUT
            echo "PIPELINE_NAME=ms-bp-pipeline-prod-standard" >> $GITHUB_OUTPUT
          fi

      - name: Package source code to ZIP
        if: github.event_name == 'push'
        run: |
          # ソースディレクトリ全体をパッケージ化する
          zip -r source-code.zip .
          # 生成されたZIPファイルを一覧表示する
          ls -lh *.zip

      - name: Assume Cross-Account Role
        if: github.event_name == 'push'
        run: |
          # 一時的な認証情報を取得する
          CREDS=$(aws sts assume-role \
            --role-arn "arn:aws:iam::${{ steps.aws-account.outputs.ACCOUNT }}:role/GitHubRunnerCrossAccountRole" \
            --role-session-name "github-actions-upload")
          
          echo "AWS_ACCESS_KEY_ID=$(echo $CREDS | jq -r .Credentials.AccessKeyId)" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=$(echo $CREDS | jq -r .Credentials.SecretAccessKey)" >> $GITHUB_ENV
          echo "AWS_SESSION_TOKEN=$(echo $CREDS | jq -r .Credentials.SessionToken)" >> $GITHUB_ENV
          
      - name: Copy artifacts to S3
        if: github.event_name == 'push'
        run: |
          # ZIPファイルをS3にアップロードする
          echo "s3://${{ steps.aws-account.outputs.S3_BUCKET }}"
          aws s3 cp *.zip s3://${{ steps.aws-account.outputs.S3_BUCKET }}/source-code-backend/ \
          --acl bucket-owner-full-control
      
          # アップロードの確認
          aws s3 ls s3://${{ steps.aws-account.outputs.S3_BUCKET }}/source-code-backend/

      - name: Trigger AWS Pipeline
        if: github.event_name == 'push'
        run: |    
          echo "PIPELINE_NAME:${{ steps.aws-account.outputs.PIPELINE_NAME }}"
          aws codepipeline start-pipeline-execution --name ${{ steps.aws-account.outputs.PIPELINE_NAME }}